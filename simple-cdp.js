/**
 * Simple Chrome DevTools Protocol implementation
 * Lightweight alternative to chrome-remote-interface
 */

import WebSocket from "ws";

export class CDP {
  constructor(targetInfo) {
    this.targetInfo = targetInfo;
    this.ws = null;
    this.messageId = 0;
    this.pendingMessages = new Map();
  }

  async connect() {
    if (this.ws) return;

    this.ws = new WebSocket(this.targetInfo.webSocketDebuggerUrl);

    return new Promise((resolve, reject) => {
      this.ws.on("open", () => {
        console.log(`Connected to target: ${this.targetInfo.title}`);
        resolve();
      });

      this.ws.on("error", reject);

      this.ws.on("message", (data) => {
        const message = JSON.parse(data.toString());

        if (message.id && this.pendingMessages.has(message.id)) {
          const { resolve, reject } = this.pendingMessages.get(message.id);
          this.pendingMessages.delete(message.id);

          if (message.error) {
            reject(new Error(message.error.message));
          } else {
            resolve(message.result);
          }
        }
      });
    });
  }

  async send(method, params = {}, sessionId = null) {
    if (!this.ws) {
      await this.connect();
    }

    const id = ++this.messageId;
    const message = { id, method, params };

    // Add sessionId if provided (for remote debugging)
    if (sessionId) {
      message.sessionId = sessionId;
    }

    return new Promise((resolve, reject) => {
      this.pendingMessages.set(id, { resolve, reject });
      this.ws.send(JSON.stringify(message));
    });
  }

  // Runtime domain
  get Runtime() {
    return {
      enable: (params = {}, sessionId = null) =>
        this.send("Runtime.enable", params, sessionId),
      evaluate: (params, sessionId = null) =>
        this.send("Runtime.evaluate", params, sessionId),
      addScriptToEvaluateOnNewDocument: (params, sessionId = null) =>
        this.send(
          "Runtime.addScriptToEvaluateOnNewDocument",
          params,
          sessionId
        ),
      removeScriptToEvaluateOnNewDocument: (params, sessionId = null) =>
        this.send(
          "Runtime.removeScriptToEvaluateOnNewDocument",
          params,
          sessionId
        ),
    };
  }

  // Page domain
  get Page() {
    return {
      enable: (params = {}, sessionId = null) =>
        this.send("Page.enable", params, sessionId),
      navigate: (params, sessionId = null) =>
        this.send("Page.navigate", params, sessionId),
    };
  }

  // Target domain
  get Target() {
    return {
      getTargets: (params = {}, sessionId = null) =>
        this.send("Target.getTargets", params, sessionId),
      createTarget: (params, sessionId = null) =>
        this.send("Target.createTarget", params, sessionId),
      attachToTarget: (params, sessionId = null) =>
        this.send("Target.attachToTarget", params, sessionId),
      closeTarget: (params, sessionId = null) =>
        this.send("Target.closeTarget", params, sessionId),
      activateTarget: (params, sessionId = null) =>
        this.send("Target.activateTarget", params, sessionId),
    };
  }

  // Input domain
  get Input() {
    return {
      dispatchKeyEvent: (params, sessionId = null) =>
        this.send("Input.dispatchKeyEvent", params, sessionId),
      dispatchMouseEvent: (params, sessionId = null) =>
        this.send("Input.dispatchMouseEvent", params, sessionId),
    };
  }

  async close() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

export async function createTarget(url) {
  // Create a new target (tab) using PUT method
  const response = await fetch(
    `http://localhost:9222/json/new?${encodeURIComponent(url)}`,
    {
      method: "PUT",
    }
  );
  const targetInfo = await response.json();
  return targetInfo;
}

export async function listTargets() {
  const response = await fetch("http://localhost:9222/json");
  const targets = await response.json();
  return targets;
}

export async function closeTarget(targetId) {
  const response = await fetch(`http://localhost:9222/json/close/${targetId}`);
  return response.text();
}

/**
 * Debug Patches for Your WebRTC Implementation
 * 
 * Add these debugging snippets to your existing code to diagnose
 * black video issues in your tab streaming system.
 */

// ============================================================================
// 1. TARGET TAB CAPTURE DEBUG (target-tab-streamer.js)
// ============================================================================

// Add this to your captureCurrentTab() method after getDisplayMedia call:
function debugCaptureCurrentTab(stream) {
  console.group("🎥 [Target Tab] Capture Debug");
  
  if (!stream) {
    console.error("❌ getDisplayMedia returned null/undefined stream");
    return false;
  }

  console.log("📊 Captured Stream:", {
    id: stream.id,
    active: stream.active,
    tracks: stream.getTracks().length
  });

  // Check each track
  stream.getTracks().forEach((track, index) => {
    console.log(`🎬 Track ${index + 1}:`, {
      kind: track.kind,
      id: track.id,
      label: track.label,
      enabled: track.enabled,
      muted: track.muted,
      readyState: track.readyState,
      settings: track.getSettings()
    });

    // Monitor track for changes
    track.addEventListener('ended', () => {
      console.error(`❌ [Target Tab] Track ${track.id} ended!`);
    });
    
    track.addEventListener('mute', () => {
      console.warn(`⚠️ [Target Tab] Track ${track.id} muted!`);
    });
  });

  // Test if we can create a video element and play the stream
  const testVideo = document.createElement('video');
  testVideo.srcObject = stream;
  testVideo.muted = true;
  testVideo.autoplay = true;
  testVideo.style.position = 'fixed';
  testVideo.style.top = '10px';
  testVideo.style.right = '10px';
  testVideo.style.width = '200px';
  testVideo.style.height = '150px';
  testVideo.style.border = '2px solid red';
  testVideo.style.zIndex = '9999';
  
  testVideo.onloadedmetadata = () => {
    console.log("✅ [Target Tab] Test video metadata loaded:", {
      width: testVideo.videoWidth,
      height: testVideo.videoHeight
    });
  };
  
  testVideo.onerror = (error) => {
    console.error("❌ [Target Tab] Test video error:", error);
  };
  
  document.body.appendChild(testVideo);
  
  // Remove test video after 5 seconds
  setTimeout(() => {
    if (testVideo.parentNode) {
      testVideo.parentNode.removeChild(testVideo);
    }
  }, 5000);

  console.groupEnd();
  return true;
}

// ============================================================================
// 2. CONTROL TAB RELAY DEBUG (control-tab-script.js)
// ============================================================================

// Add this to your ontrack handler in the control tab:
function debugControlTabRelay(event, targetPeerConnection, clientPeerConnection) {
  console.group("🔄 [Control Tab] Relay Debug");
  
  console.log("📡 Received track event:", {
    track: {
      kind: event.track.kind,
      id: event.track.id,
      enabled: event.track.enabled,
      muted: event.track.muted,
      readyState: event.track.readyState
    },
    streams: event.streams.map(s => ({
      id: s.id,
      active: s.active,
      trackCount: s.getTracks().length
    }))
  });

  const [stream] = event.streams;
  
  // Debug the relay process
  stream.getTracks().forEach((track) => {
    console.log(`🎬 Relaying track ${track.id}:`, {
      kind: track.kind,
      enabled: track.enabled,
      muted: track.muted,
      readyState: track.readyState
    });

    // Check if track is properly added to client peer connection
    setTimeout(() => {
      const senders = clientPeerConnection.getSenders();
      const trackSender = senders.find(sender => sender.track && sender.track.id === track.id);
      
      if (trackSender) {
        console.log(`✅ Track ${track.id} successfully added to client peer connection`);
      } else {
        console.error(`❌ Track ${track.id} NOT found in client peer connection senders`);
        console.log("Available senders:", senders.map(s => ({
          trackId: s.track?.id,
          trackKind: s.track?.kind
        })));
      }
    }, 100);
  });

  console.groupEnd();
}

// ============================================================================
// 3. WEB CLIENT VIDEO ELEMENT DEBUG (client.js)
// ============================================================================

// Add this to your displayStream() method:
function debugWebClientDisplay(streamId, mediaStream, videoElement) {
  console.group(`📺 [Web Client] Display Debug - ${streamId}`);
  
  if (!mediaStream) {
    console.error("❌ MediaStream is null/undefined");
    console.groupEnd();
    return false;
  }

  if (!videoElement) {
    console.error("❌ Video element is null/undefined");
    console.groupEnd();
    return false;
  }

  console.log("📊 Stream Info:", {
    id: mediaStream.id,
    active: mediaStream.active,
    tracks: mediaStream.getTracks().length
  });

  console.log("🎮 Video Element Info:", {
    autoplay: videoElement.autoplay,
    muted: videoElement.muted,
    playsinline: videoElement.playsinline,
    currentSrcObject: videoElement.srcObject?.id || "none"
  });

  // Debug each track
  mediaStream.getTracks().forEach((track, index) => {
    console.log(`🎬 Track ${index + 1}:`, {
      kind: track.kind,
      id: track.id,
      enabled: track.enabled,
      muted: track.muted,
      readyState: track.readyState
    });
  });

  // Enhanced video element event listeners
  videoElement.onloadstart = () => {
    console.log("📺 [Web Client] Video load started");
  };

  videoElement.onloadedmetadata = () => {
    console.log("📺 [Web Client] Video metadata loaded:", {
      width: videoElement.videoWidth,
      height: videoElement.videoHeight,
      duration: videoElement.duration
    });
    
    if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
      console.error("❌ [Web Client] Video dimensions are 0x0 - BLACK SCREEN DETECTED!");
    }
  };

  videoElement.oncanplay = () => {
    console.log("📺 [Web Client] Video can start playing");
  };

  videoElement.onplay = () => {
    console.log("📺 [Web Client] Video started playing");
  };

  videoElement.onpause = () => {
    console.log("📺 [Web Client] Video paused");
  };

  videoElement.onerror = (error) => {
    console.error("❌ [Web Client] Video error:", error);
    console.error("Video error details:", {
      error: videoElement.error,
      networkState: videoElement.networkState,
      readyState: videoElement.readyState
    });
  };

  videoElement.onstalled = () => {
    console.warn("⚠️ [Web Client] Video stalled");
  };

  videoElement.onwaiting = () => {
    console.warn("⚠️ [Web Client] Video waiting for data");
  };

  // Monitor video element state periodically
  const monitorInterval = setInterval(() => {
    if (!videoElement.parentNode) {
      clearInterval(monitorInterval);
      return;
    }

    const state = {
      readyState: videoElement.readyState,
      networkState: videoElement.networkState,
      currentTime: videoElement.currentTime,
      paused: videoElement.paused,
      ended: videoElement.ended,
      videoWidth: videoElement.videoWidth,
      videoHeight: videoElement.videoHeight
    };

    // Only log if there are issues
    if (state.videoWidth === 0 || state.videoHeight === 0) {
      console.warn("⚠️ [Web Client] Video dimensions still 0x0:", state);
    }
    
    if (state.readyState < 2) {
      console.warn("⚠️ [Web Client] Video not ready:", state);
    }
  }, 2000);

  // Clear monitor after 30 seconds
  setTimeout(() => clearInterval(monitorInterval), 30000);

  console.groupEnd();
  return true;
}

// ============================================================================
// 4. QUICK DIAGNOSTIC FUNCTIONS
// ============================================================================

// Add these to browser console for quick debugging:

// Check all video elements on page
function debugAllVideoElements() {
  const videos = document.querySelectorAll('video');
  console.group(`📺 Found ${videos.length} video elements`);
  
  videos.forEach((video, index) => {
    console.log(`Video ${index + 1}:`, {
      srcObject: video.srcObject?.id || "none",
      autoplay: video.autoplay,
      muted: video.muted,
      paused: video.paused,
      readyState: video.readyState,
      videoWidth: video.videoWidth,
      videoHeight: video.videoHeight,
      currentTime: video.currentTime
    });
  });
  
  console.groupEnd();
}

// Check WebRTC peer connections
function debugPeerConnections() {
  // This will work if you expose peer connections globally
  if (window.peerConnections) {
    console.group("🔗 Peer Connections Debug");
    
    window.peerConnections.forEach((pc, id) => {
      console.log(`Peer Connection ${id}:`, {
        connectionState: pc.connectionState,
        iceConnectionState: pc.iceConnectionState,
        signalingState: pc.signalingState,
        senders: pc.getSenders().length,
        receivers: pc.getReceivers().length
      });
    });
    
    console.groupEnd();
  }
}

// Force video element refresh
function forceVideoRefresh(videoElement) {
  if (!videoElement || !videoElement.srcObject) return;
  
  console.log("🔄 Forcing video refresh...");
  const stream = videoElement.srcObject;
  videoElement.srcObject = null;
  
  setTimeout(() => {
    videoElement.srcObject = stream;
    videoElement.play().catch(e => console.error("Play failed:", e));
  }, 100);
}

// Export functions for global use
if (typeof window !== 'undefined') {
  window.debugCaptureCurrentTab = debugCaptureCurrentTab;
  window.debugControlTabRelay = debugControlTabRelay;
  window.debugWebClientDisplay = debugWebClientDisplay;
  window.debugAllVideoElements = debugAllVideoElements;
  window.debugPeerConnections = debugPeerConnections;
  window.forceVideoRefresh = forceVideoRefresh;
}

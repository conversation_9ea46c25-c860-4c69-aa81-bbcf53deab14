# WebRTC Black Video Debugging Tools & Commands

## 🔧 Browser Console Commands

### 1. Quick Video Element Inspection

```javascript
// Check all video elements on the page
document.querySelectorAll('video').forEach((video, i) => {
  console.log(`Video ${i+1}:`, {
    srcObject: video.srcObject?.id || 'none',
    autoplay: video.autoplay,
    muted: video.muted,
    paused: video.paused,
    readyState: video.readyState,
    videoWidth: video.videoWidth,
    videoHeight: video.videoHeight,
    currentTime: video.currentTime,
    error: video.error
  });
});
```

### 2. Stream and Track Analysis

```javascript
// Analyze a specific video element's stream
function analyzeVideoStream(videoElement) {
  if (!videoElement.srcObject) {
    console.error('No srcObject found');
    return;
  }
  
  const stream = videoElement.srcObject;
  console.log('Stream Analysis:', {
    id: stream.id,
    active: stream.active,
    tracks: stream.getTracks().map(track => ({
      kind: track.kind,
      id: track.id,
      enabled: track.enabled,
      muted: track.muted,
      readyState: track.readyState,
      label: track.label,
      settings: track.getSettings()
    }))
  });
}

// Usage: analyzeVideoStream(document.querySelector('video'));
```

### 3. WebRTC Stats Monitoring

```javascript
// Monitor WebRTC statistics for video tracks
async function monitorWebRTCStats(peerConnection) {
  const stats = await peerConnection.getStats();
  
  stats.forEach(report => {
    if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
      console.log('📥 Inbound Video Stats:', {
        framesReceived: report.framesReceived,
        framesDropped: report.framesDropped,
        frameWidth: report.frameWidth,
        frameHeight: report.frameHeight,
        framesPerSecond: report.framesPerSecond,
        bytesReceived: report.bytesReceived
      });
    }
    
    if (report.type === 'outbound-rtp' && report.mediaType === 'video') {
      console.log('📤 Outbound Video Stats:', {
        framesSent: report.framesSent,
        frameWidth: report.frameWidth,
        frameHeight: report.frameHeight,
        framesPerSecond: report.framesPerSecond,
        bytesSent: report.bytesSent
      });
    }
  });
}
```

### 4. Force Video Refresh

```javascript
// Force refresh a video element
function refreshVideo(videoElement) {
  const stream = videoElement.srcObject;
  videoElement.srcObject = null;
  setTimeout(() => {
    videoElement.srcObject = stream;
    videoElement.play().catch(e => console.error('Play failed:', e));
  }, 100);
}
```

## 🛠️ Chrome DevTools Features

### 1. WebRTC Internals
- Navigate to `chrome://webrtc-internals/`
- Monitor real-time WebRTC connection stats
- Check for dropped frames, resolution changes, and connection issues

### 2. Media Tab in DevTools
- Open DevTools (F12) → Sources → Media tab
- Shows all media elements and their states
- Displays video/audio track information

### 3. Network Tab Analysis
- Monitor WebSocket connections for signaling
- Check for failed ICE candidates
- Verify STUN/TURN server connectivity

## 🔍 Specific Debugging Steps for Your Implementation

### Step 1: Target Tab Capture Verification

```javascript
// In target tab console (where getDisplayMedia is called)
navigator.mediaDevices.getDisplayMedia({
  video: { width: 1280, height: 720 },
  preferCurrentTab: true
}).then(stream => {
  console.log('✅ Capture successful:', {
    id: stream.id,
    active: stream.active,
    tracks: stream.getTracks().length
  });
  
  // Create test video to verify content
  const testVideo = document.createElement('video');
  testVideo.srcObject = stream;
  testVideo.autoplay = true;
  testVideo.muted = true;
  testVideo.style.position = 'fixed';
  testVideo.style.top = '10px';
  testVideo.style.right = '10px';
  testVideo.style.width = '200px';
  testVideo.style.zIndex = '9999';
  testVideo.style.border = '2px solid red';
  
  testVideo.onloadedmetadata = () => {
    console.log('Test video dimensions:', testVideo.videoWidth, 'x', testVideo.videoHeight);
  };
  
  document.body.appendChild(testVideo);
}).catch(console.error);
```

### Step 2: Control Tab Relay Verification

```javascript
// In control tab console
// Check if tracks are properly forwarded
function checkTrackRelay(sourcePeerConnection, targetPeerConnection) {
  const sourceSenders = sourcePeerConnection.getSenders();
  const targetSenders = targetPeerConnection.getSenders();
  
  console.log('Source senders:', sourceSenders.length);
  console.log('Target senders:', targetSenders.length);
  
  sourceSenders.forEach((sender, i) => {
    if (sender.track) {
      console.log(`Source track ${i}:`, {
        kind: sender.track.kind,
        id: sender.track.id,
        enabled: sender.track.enabled,
        readyState: sender.track.readyState
      });
    }
  });
  
  targetSenders.forEach((sender, i) => {
    if (sender.track) {
      console.log(`Target track ${i}:`, {
        kind: sender.track.kind,
        id: sender.track.id,
        enabled: sender.track.enabled,
        readyState: sender.track.readyState
      });
    }
  });
}
```

### Step 3: Web Client Video Element Verification

```javascript
// In web client console
function diagnoseBlackVideo() {
  const videos = document.querySelectorAll('video');
  
  videos.forEach((video, i) => {
    console.group(`Video Element ${i + 1} Diagnosis`);
    
    // Basic checks
    console.log('Basic Info:', {
      hasStream: !!video.srcObject,
      autoplay: video.autoplay,
      muted: video.muted,
      paused: video.paused,
      readyState: video.readyState,
      dimensions: `${video.videoWidth}x${video.videoHeight}`
    });
    
    // Stream checks
    if (video.srcObject) {
      const stream = video.srcObject;
      console.log('Stream Info:', {
        id: stream.id,
        active: stream.active,
        videoTracks: stream.getVideoTracks().length,
        audioTracks: stream.getAudioTracks().length
      });
      
      stream.getVideoTracks().forEach((track, j) => {
        console.log(`Video Track ${j + 1}:`, {
          id: track.id,
          enabled: track.enabled,
          muted: track.muted,
          readyState: track.readyState,
          settings: track.getSettings()
        });
      });
    }
    
    // Common issues
    const issues = [];
    if (!video.srcObject) issues.push('No stream assigned');
    if (video.videoWidth === 0) issues.push('Zero width');
    if (video.videoHeight === 0) issues.push('Zero height');
    if (video.readyState < 2) issues.push('Not ready');
    if (video.paused && !video.autoplay) issues.push('Paused without autoplay');
    
    if (issues.length > 0) {
      console.error('❌ Issues found:', issues);
    } else {
      console.log('✅ No obvious issues');
    }
    
    console.groupEnd();
  });
}

// Run diagnosis
diagnoseBlackVideo();
```

## 🎯 Common Fix Attempts

### 1. Video Element Attribute Fix

```javascript
// Ensure proper video element setup
function fixVideoElement(videoElement) {
  videoElement.autoplay = true;
  videoElement.muted = true;
  videoElement.playsinline = true;
  videoElement.controls = false;
  
  // Force play
  videoElement.play().catch(e => console.error('Play failed:', e));
}
```

### 2. Stream Reassignment Fix

```javascript
// Sometimes reassigning the stream helps
function reassignStream(videoElement) {
  if (!videoElement.srcObject) return;
  
  const stream = videoElement.srcObject;
  videoElement.srcObject = null;
  
  requestAnimationFrame(() => {
    videoElement.srcObject = stream;
    videoElement.play().catch(e => console.error('Play failed:', e));
  });
}
```

### 3. Track Replacement Fix

```javascript
// Replace tracks if they're ended or muted
function replaceEndedTracks(stream, newStream) {
  const oldTracks = stream.getVideoTracks();
  const newTracks = newStream.getVideoTracks();
  
  oldTracks.forEach((oldTrack, i) => {
    if (oldTrack.readyState === 'ended' && newTracks[i]) {
      stream.removeTrack(oldTrack);
      stream.addTrack(newTracks[i]);
      console.log('Replaced ended track:', oldTrack.id, '→', newTracks[i].id);
    }
  });
}
```

## 📊 Monitoring Script

```javascript
// Continuous monitoring for debugging
function startVideoMonitoring() {
  setInterval(() => {
    const videos = document.querySelectorAll('video');
    
    videos.forEach((video, i) => {
      if (video.srcObject && (video.videoWidth === 0 || video.videoHeight === 0)) {
        console.warn(`⚠️ Video ${i + 1} has black screen (${video.videoWidth}x${video.videoHeight})`);
        
        // Auto-fix attempt
        if (video.readyState >= 2) {
          console.log('Attempting auto-fix...');
          reassignStream(video);
        }
      }
    });
  }, 5000);
}

// Start monitoring
startVideoMonitoring();
```

## 🚀 Quick Test Commands

```javascript
// Test 1: Check if getUserMedia works in current context
navigator.mediaDevices.getUserMedia({video: true})
  .then(stream => {
    console.log('✅ getUserMedia works');
    stream.getTracks().forEach(track => track.stop());
  })
  .catch(e => console.error('❌ getUserMedia failed:', e));

// Test 2: Check if getDisplayMedia works
navigator.mediaDevices.getDisplayMedia({video: true})
  .then(stream => {
    console.log('✅ getDisplayMedia works');
    stream.getTracks().forEach(track => track.stop());
  })
  .catch(e => console.error('❌ getDisplayMedia failed:', e));

// Test 3: Create and test a simple video element
const testStream = new MediaStream();
const testVideo = document.createElement('video');
testVideo.srcObject = testStream;
console.log('Test video created:', testVideo.readyState);
```

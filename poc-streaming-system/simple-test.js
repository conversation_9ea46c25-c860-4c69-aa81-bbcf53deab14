/**
 * Simple Test Script for POC Streaming System
 * 
 * This script helps test the system step by step without complex dependencies
 */

import { BrowserManager } from './browser-manager.js';
import { SignalingServer } from './signaling-server.js';
import { ScriptInjector } from './script-injector.js';
import WebSocket from 'ws';

class SimpleTest {
  constructor() {
    this.browserManager = null;
    this.signalingServer = null;
    this.scriptInjector = null;
    
    this.config = {
      browserPort: 9222,
      signalingPort: 8080,
      headless: false
    };
  }

  async runTest() {
    console.log('🧪 Starting Simple POC Streaming System Test...\n');
    
    try {
      // Test 1: Browser Manager
      await this.testBrowserManager();
      
      // Test 2: Signaling Server
      await this.testSignalingServer();
      
      // Test 3: Script Injection
      await this.testScriptInjection();
      
      // Test 4: WebSocket Connection
      await this.testWebSocketConnection();
      
      console.log('\n✅ All tests completed successfully!');
      console.log('\n🎯 Next steps:');
      console.log('1. Keep this test running');
      console.log('2. Open http://localhost:8080 in your browser (if you have a simple HTTP server)');
      console.log('3. Or manually test WebSocket connections');
      
      // Keep test running for manual inspection
      console.log('\nPress Ctrl+C to stop the test...');
      await this.keepAlive();
      
    } catch (error) {
      console.error('❌ Test failed:', error);
      await this.cleanup();
      process.exit(1);
    }
  }

  async testBrowserManager() {
    console.log('🔬 Test 1: Browser Manager');
    console.log('─'.repeat(30));
    
    try {
      this.browserManager = new BrowserManager({
        port: this.config.browserPort,
        headless: this.config.headless
      });
      
      console.log('  📋 Launching Chrome browser...');
      await this.browserManager.launchBrowser();
      console.log('  ✅ Chrome launched successfully');
      
      console.log('  📋 Creating control tab...');
      await this.browserManager.createControlTab();
      console.log('  ✅ Control tab created');
      
      console.log('  📋 Creating target tab...');
      const targetTab = await this.browserManager.createTargetTab('https://example.com');
      console.log(`  ✅ Target tab created: ${targetTab.id}`);
      
      console.log('  📋 Listing all targets...');
      const targets = await this.browserManager.getTargets();
      console.log(`  ✅ Found ${targets.length} targets`);
      
      console.log('✅ Browser Manager test passed\n');
      
    } catch (error) {
      console.error('❌ Browser Manager test failed:', error.message);
      throw error;
    }
  }

  async testSignalingServer() {
    console.log('🔬 Test 2: Signaling Server');
    console.log('─'.repeat(30));
    
    try {
      this.signalingServer = new SignalingServer({
        port: this.config.signalingPort
      });
      
      console.log('  📡 Starting signaling server...');
      await this.signalingServer.start();
      console.log(`  ✅ Signaling server started on port ${this.config.signalingPort}`);
      
      console.log('  📊 Checking server stats...');
      const stats = this.signalingServer.getStats();
      console.log(`  ✅ Server stats: ${JSON.stringify(stats)}`);
      
      console.log('✅ Signaling Server test passed\n');
      
    } catch (error) {
      console.error('❌ Signaling Server test failed:', error.message);
      throw error;
    }
  }

  async testScriptInjection() {
    console.log('🔬 Test 3: Script Injection');
    console.log('─'.repeat(30));
    
    try {
      this.scriptInjector = new ScriptInjector(
        this.browserManager,
        this.signalingServer
      );
      
      console.log('  💉 Injecting control tab script...');
      const signalingUrl = `ws://localhost:${this.config.signalingPort}`;
      await this.scriptInjector.injectControlTabScript(signalingUrl);
      console.log('  ✅ Control tab script injected');
      
      console.log('  💉 Injecting target tab script...');
      const targetTabs = this.browserManager.getAllTargetTabs();
      if (targetTabs.length > 0) {
        const targetTab = targetTabs[0];
        await this.scriptInjector.injectScript(targetTab.id, signalingUrl);
        console.log(`  ✅ Target tab script injected: ${targetTab.id}`);
      } else {
        console.log('  ⚠️  No target tabs found for script injection');
      }
      
      console.log('✅ Script Injection test passed\n');
      
    } catch (error) {
      console.error('❌ Script Injection test failed:', error.message);
      throw error;
    }
  }

  async testWebSocketConnection() {
    console.log('🔬 Test 4: WebSocket Connection');
    console.log('─'.repeat(30));
    
    try {
      console.log('  🔌 Testing WebSocket connection...');
      
      const ws = new WebSocket(`ws://localhost:${this.config.signalingPort}`);
      
      const connectionTest = new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 5000);
        
        ws.on('open', () => {
          clearTimeout(timeout);
          console.log('  ✅ WebSocket connected successfully');
          
          // Send test message
          ws.send(JSON.stringify({
            type: 'register-web-client',
            metadata: { test: true }
          }));
          
          console.log('  ✅ Test message sent');
        });
        
        ws.on('message', (data) => {
          try {
            const message = JSON.parse(data.toString());
            console.log(`  📨 Received message: ${message.type}`);
            
            if (message.type === 'welcome') {
              console.log(`  ✅ Welcome message received: ${message.clientId}`);
              ws.close();
              resolve();
            }
          } catch (error) {
            console.error('  ❌ Failed to parse message:', error);
          }
        });
        
        ws.on('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
        
        ws.on('close', () => {
          console.log('  🔌 WebSocket connection closed');
          resolve();
        });
      });
      
      await connectionTest;
      console.log('✅ WebSocket Connection test passed\n');
      
    } catch (error) {
      console.error('❌ WebSocket Connection test failed:', error.message);
      throw error;
    }
  }

  async keepAlive() {
    return new Promise((resolve) => {
      process.on('SIGINT', async () => {
        console.log('\n👋 Shutting down test...');
        await this.cleanup();
        resolve();
        process.exit(0);
      });
    });
  }

  async cleanup() {
    console.log('🧹 Cleaning up test resources...');
    
    try {
      if (this.scriptInjector) {
        await this.scriptInjector.cleanup();
      }
      
      if (this.browserManager) {
        await this.browserManager.cleanup();
      }
      
      if (this.signalingServer) {
        await this.signalingServer.stop();
      }
      
      console.log('✅ Cleanup completed');
      
    } catch (error) {
      console.error('❌ Cleanup error:', error);
    }
  }

  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new SimpleTest();
  test.runTest().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

/**
 * Target Tab Streamer Script
 *
 * This script is injected into target tabs to automatically capture their content
 * and stream it via WebRTC. Uses preferCurrentTab: true for automatic capture.
 */

(() => {
  "use strict";

  // Prevent multiple injections
  if (window.POCTargetStreamer) {
    console.log("[POC-Streamer] Target streamer already initialized");
    return;
  }

  class TargetTabStreamer {
    constructor(signalingServerUrl) {
      this.signalingServerUrl = signalingServerUrl;
      this.ws = null;
      this.clientId = null;
      this.tabId = null;
      this.isConnected = false;
      this.captureStream = null;
      this.peerConnections = new Map(); // streamId -> RTCPeerConnection

      // Debug properties
      this.debug = true;
      this.debugTestVideo = null;
      this.trackMonitors = new Map(); // trackId -> intervalId
      this.captureAttempts = 0;

      console.log("[POC-Streamer] Target tab streamer initializing...");
      this.init();
    }

    async init() {
      try {
        // Get tab ID from URL or generate one
        this.tabId = this.getTabId();

        // Connect to signaling server
        await this.connectToSignalingServer();

        console.log(
          "[POC-Streamer] Target tab streamer initialized successfully"
        );
      } catch (error) {
        console.error(
          "[POC-Streamer] Failed to initialize target streamer:",
          error
        );
      }
    }

    getTabId() {
      // Try to get tab ID from various sources
      const urlParams = new URLSearchParams(window.location.search);
      const tabId =
        urlParams.get("tabId") ||
        window.name ||
        document.title ||
        window.location.hostname ||
        this.generateId();

      console.log("[POC-Streamer] Tab ID:", tabId);
      return tabId;
    }

    async connectToSignalingServer() {
      return new Promise((resolve, reject) => {
        this.ws = new WebSocket(this.signalingServerUrl);

        this.ws.onopen = () => {
          console.log("[POC-Streamer] Connected to signaling server");
          this.isConnected = true;

          // Register as target tab
          this.sendMessage({
            type: "register-target-tab",
            tabId: this.tabId,
            url: window.location.href,
            title: document.title,
            metadata: {
              userAgent: navigator.userAgent,
              timestamp: Date.now(),
            },
          });

          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error("[POC-Streamer] Failed to parse message:", error);
          }
        };

        this.ws.onclose = () => {
          console.log("[POC-Streamer] Disconnected from signaling server");
          this.isConnected = false;

          // Attempt to reconnect after delay
          setTimeout(() => {
            if (!this.isConnected) {
              console.log("[POC-Streamer] Attempting to reconnect...");
              this.connectToSignalingServer().catch(console.error);
            }
          }, 5000);
        };

        this.ws.onerror = (error) => {
          console.error("[POC-Streamer] WebSocket error:", error);
          reject(error);
        };

        // Timeout for connection
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error("Connection timeout"));
          }
        }, 10000);
      });
    }

    async handleMessage(message) {
      console.log("[POC-Streamer] Received message:", message.type);

      switch (message.type) {
        case "welcome":
          this.clientId = message.clientId;
          console.log(
            "[POC-Streamer] Registered with client ID:",
            this.clientId
          );
          break;

        case "start-streaming":
          await this.handleStartStream(message);
          break;

        case "webrtc-answer-to-target":
          await this.handleWebRTCAnswer(message);
          break;

        case "webrtc-ice-candidate-to-target":
          await this.handleICECandidate(message);
          break;

        default:
          console.log("[POC-Streamer] Unhandled message type:", message.type);
      }
    }

    async handleStartStream(message) {
      try {
        console.log("[POC-Streamer] Starting stream for:", message);

        // Capture current tab content automatically
        const stream = await this.captureCurrentTab();
        this.captureStream = stream;

        // Create peer connection for this stream
        const peerConnection = await this.createPeerConnection(message);

        // Add stream tracks to peer connection
        stream.getTracks().forEach((track) => {
          peerConnection.addTrack(track, stream);
        });

        // Create and send offer
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);

        this.sendMessage({
          type: "webrtc-offer-from-target",
          offer: offer,
          streamId: message.streamId,
          targetClientId: message.requestedBy,
        });

        // Notify server that streaming is ready
        this.sendMessage({
          type: "streaming-ready",
          streamId: message.streamId,
          tabId: this.tabId,
        });

        console.log("[POC-Streamer] Stream started and offer sent");
      } catch (error) {
        console.error("[POC-Streamer] Failed to start stream:", error);

        // Send error back to signaling server
        this.sendMessage({
          type: "stream-error",
          streamId: message.streamId,
          error: error.message,
        });
      }
    }

    async captureCurrentTab() {
      this.captureAttempts++;
      console.group(
        `🎥 [POC-Streamer] Capture Attempt #${this.captureAttempts}`
      );

      try {
        console.log("[POC-Streamer] Capturing current tab content...");
        console.log("[POC-Streamer] Page info:", {
          url: window.location.href,
          title: document.title,
          visible: !document.hidden,
          focused: document.hasFocus(),
          readyState: document.readyState,
        });

        // Use getDisplayMedia with preferCurrentTab for automatic capture
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 30 },
          },
          audio: false, // Disable audio for simplicity
          preferCurrentTab: true, // Automatically capture this tab
        });

        console.log("✅ [POC-Streamer] getDisplayMedia successful");

        // Enhanced stream debugging
        const streamDebugInfo = await this.debugCapturedStream(stream);

        // Create test video element to verify content
        await this.createDebugTestVideo(stream);

        // Monitor tracks for changes
        this.monitorStreamTracks(stream);

        console.groupEnd();
        return stream;
      } catch (error) {
        console.error(
          "❌ [POC-Streamer] Failed to capture tab content:",
          error
        );
        console.error("[POC-Streamer] Error details:", {
          name: error.name,
          message: error.message,
          stack: error.stack,
        });

        console.groupEnd();
        // Fallback to a simple canvas stream
        return this.createFallbackStream();
      }
    }

    createFallbackStream() {
      console.log("[POC-Streamer] Creating fallback stream...");

      const canvas = document.createElement("canvas");
      canvas.width = 640;
      canvas.height = 480;
      const ctx = canvas.getContext("2d");

      // Create animated fallback content
      let frame = 0;
      const animate = () => {
        ctx.fillStyle = "#2196F3";
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = "white";
        ctx.font = "24px Arial";
        ctx.textAlign = "center";
        ctx.fillText(
          "Tab Content Capture",
          canvas.width / 2,
          canvas.height / 2 - 40
        );
        ctx.fillText(`${document.title}`, canvas.width / 2, canvas.height / 2);
        ctx.fillText(
          `Frame: ${frame++}`,
          canvas.width / 2,
          canvas.height / 2 + 40
        );

        requestAnimationFrame(animate);
      };
      animate();

      return canvas.captureStream(30);
    }

    async createPeerConnection(message) {
      const peerConnection = new RTCPeerConnection({
        iceServers: [{ urls: "stun:stun.l.google.com:19302" }],
      });

      // Store peer connection
      this.peerConnections.set(message.streamId, peerConnection);

      // Handle ICE candidates
      peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendMessage({
            type: "webrtc-ice-candidate-from-target",
            candidate: event.candidate,
            streamId: message.streamId,
            targetClientId: message.requestedBy,
          });
        }
      };

      // Handle connection state changes
      peerConnection.onconnectionstatechange = () => {
        console.log(
          "[POC-Streamer] Connection state:",
          peerConnection.connectionState
        );
      };

      return peerConnection;
    }

    async handleWebRTCAnswer(message) {
      const peerConnection = this.peerConnections.get(message.streamId);
      if (peerConnection) {
        await peerConnection.setRemoteDescription(message.answer);
        console.log("[POC-Streamer] WebRTC answer processed");
      }
    }

    async handleICECandidate(message) {
      const peerConnection = this.peerConnections.get(message.streamId);
      if (peerConnection) {
        await peerConnection.addIceCandidate(message.candidate);
      }
    }

    sendMessage(message) {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(message));
      }
    }

    async debugCapturedStream(stream) {
      console.group("📊 [POC-Streamer] Stream Debug Analysis");

      if (!stream) {
        console.error("❌ Stream is null/undefined");
        console.groupEnd();
        return { valid: false, reason: "null_stream" };
      }

      const debugInfo = {
        id: stream.id,
        active: stream.active,
        tracks: stream.getTracks().length,
        videoTracks: stream.getVideoTracks().length,
        audioTracks: stream.getAudioTracks().length,
        timestamp: Date.now(),
      };

      console.log("📊 Stream Info:", debugInfo);

      // Debug each track in detail
      stream.getTracks().forEach((track, index) => {
        const trackInfo = {
          index: index,
          kind: track.kind,
          id: track.id,
          label: track.label,
          enabled: track.enabled,
          muted: track.muted,
          readyState: track.readyState,
          settings: track.getSettings ? track.getSettings() : "N/A",
          constraints: track.getConstraints ? track.getConstraints() : "N/A",
        };

        console.log(`🎬 Track ${index + 1} (${track.kind}):`, trackInfo);

        // Check for common issues
        const issues = [];
        if (track.readyState === "ended") issues.push("Track is ended");
        if (track.muted) issues.push("Track is muted");
        if (!track.enabled) issues.push("Track is disabled");
        if (track.kind === "video" && track.label === "")
          issues.push("No video source label");

        if (issues.length > 0) {
          console.warn(`⚠️ Track ${index + 1} issues:`, issues);
          debugInfo[`track${index}_issues`] = issues;
        } else {
          console.log(`✅ Track ${index + 1} looks healthy`);
        }
      });

      console.groupEnd();
      return debugInfo;
    }

    async createDebugTestVideo(stream) {
      console.group("🎮 [POC-Streamer] Creating Debug Test Video");

      try {
        // Remove existing test video if any
        if (this.debugTestVideo && this.debugTestVideo.parentNode) {
          this.debugTestVideo.parentNode.removeChild(this.debugTestVideo);
        }

        // Create test video element
        this.debugTestVideo = document.createElement("video");
        this.debugTestVideo.id = "poc-debug-test-video";
        this.debugTestVideo.srcObject = stream;
        this.debugTestVideo.autoplay = true;
        this.debugTestVideo.muted = true;
        this.debugTestVideo.playsinline = true;

        // Style for visibility
        this.debugTestVideo.style.position = "fixed";
        this.debugTestVideo.style.top = "10px";
        this.debugTestVideo.style.right = "10px";
        this.debugTestVideo.style.width = "200px";
        this.debugTestVideo.style.height = "150px";
        this.debugTestVideo.style.border = "3px solid #ff4444";
        this.debugTestVideo.style.zIndex = "999999";
        this.debugTestVideo.style.backgroundColor = "#000";

        // Add debug overlay
        const overlay = document.createElement("div");
        overlay.style.position = "absolute";
        overlay.style.top = "0";
        overlay.style.left = "0";
        overlay.style.background = "rgba(255,68,68,0.8)";
        overlay.style.color = "white";
        overlay.style.padding = "2px 5px";
        overlay.style.fontSize = "10px";
        overlay.style.fontFamily = "monospace";
        overlay.textContent = "DEBUG VIDEO";

        const container = document.createElement("div");
        container.style.position = "relative";
        container.appendChild(this.debugTestVideo);
        container.appendChild(overlay);

        // Enhanced event listeners
        this.debugTestVideo.onloadstart = () => {
          console.log("🎮 [Debug Video] Load started");
        };

        this.debugTestVideo.onloadedmetadata = () => {
          const dimensions = {
            width: this.debugTestVideo.videoWidth,
            height: this.debugTestVideo.videoHeight,
            duration: this.debugTestVideo.duration,
          };
          console.log("🎮 [Debug Video] Metadata loaded:", dimensions);

          if (dimensions.width === 0 || dimensions.height === 0) {
            console.error(
              "❌ [Debug Video] ZERO DIMENSIONS DETECTED - BLACK SCREEN LIKELY!"
            );
            overlay.style.background = "rgba(255,0,0,0.9)";
            overlay.textContent = "BLACK SCREEN!";
          } else {
            console.log("✅ [Debug Video] Valid dimensions detected");
            overlay.style.background = "rgba(0,255,0,0.8)";
            overlay.textContent = `${dimensions.width}x${dimensions.height}`;
          }
        };

        this.debugTestVideo.oncanplay = () => {
          console.log("🎮 [Debug Video] Can play");
        };

        this.debugTestVideo.onplay = () => {
          console.log("🎮 [Debug Video] Started playing");
        };

        this.debugTestVideo.onerror = (error) => {
          console.error("❌ [Debug Video] Error:", error);
          overlay.style.background = "rgba(255,0,0,0.9)";
          overlay.textContent = "ERROR!";
        };

        this.debugTestVideo.onstalled = () => {
          console.warn("⚠️ [Debug Video] Stalled");
        };

        // Add to page
        document.body.appendChild(container);

        // Auto-remove after 30 seconds
        setTimeout(() => {
          if (container.parentNode) {
            container.parentNode.removeChild(container);
            console.log("🎮 [Debug Video] Auto-removed after 30s");
          }
        }, 30000);

        console.log("🎮 [Debug Video] Test video created and added to page");
        console.groupEnd();
      } catch (error) {
        console.error("❌ [Debug Video] Failed to create test video:", error);
        console.groupEnd();
      }
    }

    monitorStreamTracks(stream) {
      console.log("🔍 [POC-Streamer] Starting track monitoring...");

      stream.getTracks().forEach((track, index) => {
        const trackId = track.id;

        // Add event listeners
        track.addEventListener("ended", () => {
          console.error(
            `❌ [Track Monitor] Track ${trackId} (${track.kind}) ended!`
          );
        });

        track.addEventListener("mute", () => {
          console.warn(
            `⚠️ [Track Monitor] Track ${trackId} (${track.kind}) muted!`
          );
        });

        track.addEventListener("unmute", () => {
          console.log(
            `✅ [Track Monitor] Track ${trackId} (${track.kind}) unmuted!`
          );
        });

        // Periodic state monitoring
        const monitorInterval = setInterval(() => {
          const currentState = {
            enabled: track.enabled,
            muted: track.muted,
            readyState: track.readyState,
          };

          // Log state changes or issues
          if (track.readyState === "ended") {
            console.error(`❌ [Track Monitor] Track ${trackId} is ended!`);
            clearInterval(monitorInterval);
            this.trackMonitors.delete(trackId);
          } else if (track.muted) {
            console.warn(`⚠️ [Track Monitor] Track ${trackId} is muted`);
          }
        }, 2000);

        this.trackMonitors.set(trackId, monitorInterval);
      });
    }

    generateId() {
      return Math.random().toString(36).substring(2, 9);
    }

    cleanup() {
      console.log("[POC-Streamer] Cleaning up target streamer...");

      // Stop track monitors
      for (const [trackId, intervalId] of this.trackMonitors) {
        clearInterval(intervalId);
      }
      this.trackMonitors.clear();

      // Remove debug test video
      if (this.debugTestVideo && this.debugTestVideo.parentNode) {
        this.debugTestVideo.parentNode.removeChild(this.debugTestVideo);
        this.debugTestVideo = null;
      }

      // Stop capture stream
      if (this.captureStream) {
        this.captureStream.getTracks().forEach((track) => track.stop());
      }

      // Close peer connections
      for (const [, pc] of this.peerConnections) {
        pc.close();
      }
      this.peerConnections.clear();

      // Close WebSocket
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  // Initialize the target tab streamer
  const signalingServerUrl = "${SIGNALING_SERVER_URL}";
  window.POCTargetStreamer = new TargetTabStreamer(signalingServerUrl);

  // Cleanup on page unload
  window.addEventListener("beforeunload", () => {
    if (window.POCTargetStreamer) {
      window.POCTargetStreamer.cleanup();
    }
  });
})();

/**
 * Browser Management System for POC Streaming
 *
 * Manages CDP-enabled Chrome browser instance and control tab creation
 * Uses simple-cdp for lightweight CDP interactions
 */

import { CDP, createTarget, listTargets, closeTarget } from "../simple-cdp.js";
import puppeteer from "puppeteer";

export class BrowserManager {
  constructor(options = {}) {
    this.options = {
      port: options.port || 9222,
      userDataDir: options.userDataDir || "./chrome-data",
      headless: options.headless || false,
      ...options,
    };

    this.chromeProcess = null;
    this.controlTab = null;
    this.targetTabs = new Map(); // tabId -> { cdp, url, isInjected }
    this.isRunning = false;
  }

  /**
   * Launch Chrome with CDP enabled
   */
  async launchBrowser() {
    if (this.isRunning) {
      console.log("Browser already running");
      return;
    }

    console.log("🚀 Launching Chrome with CDP enabled...");

    // Use Puppeteer to launch Chrome with the correct flags
    this.browser = await puppeteer.launch({
      headless: this.options.headless,
      devtools: false,
      args: [
        `--user-data-dir=${this.options.userDataDir}`,
        `--remote-debugging-port=${this.options.port}`,
        "--auto-accept-this-tab-capture",
        "--remote-allow-origins=*",
      ],
      defaultViewport: null,
    });

    this.isRunning = true;

    // Wait for Chrome to be ready
    await this.waitForChromeReady();

    console.log("✅ Chrome launched successfully");
  }

  /**
   * Wait for Chrome to be ready
   */
  async waitForChromeReady() {
    const maxAttempts = 30;
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const response = await fetch(
          `http://localhost:${this.options.port}/json/version`
        );
        if (response.ok) {
          const data = await response.json();
          console.log(`Chrome ready: ${data.Browser}`);
          return;
        }
      } catch (error) {
        // Chrome not ready yet
      }

      await this.wait(1000);
      attempts++;
    }

    throw new Error("Chrome failed to start within timeout");
  }

  /**
   * Wait utility
   */
  wait(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Create and setup the control tab
   */
  async createControlTab() {
    if (this.controlTab) {
      console.log("Control tab already exists");
      return this.controlTab;
    }

    console.log("� Creating control tab...");

    // Create a new tab for control
    const targetInfo = await createTarget("about:blank");
    const cdp = new CDP(targetInfo);

    // Enable necessary domains
    await cdp.Runtime.enable();
    await cdp.Page.enable();

    this.controlTab = {
      id: targetInfo.id,
      cdp,
      targetInfo,
      connectedClients: new Set(),
      activeStreams: new Map(), // streamId -> targetTabId
    };

    console.log(`✅ Control tab created: ${targetInfo.id}`);
    return this.controlTab;
  }

  /**
   * Create a target tab for streaming
   */
  async createTargetTab(url) {
    console.log(`📑 Creating target tab for: ${url}`);

    const targetInfo = await createTarget(url);
    const cdp = new CDP(targetInfo);

    // Enable necessary domains
    await cdp.Runtime.enable();
    await cdp.Page.enable();

    const targetTab = {
      id: targetInfo.id,
      cdp,
      targetInfo,
      url,
      isInjected: false,
      streamId: null,
    };

    this.targetTabs.set(targetInfo.id, targetTab);

    console.log(`✅ Target tab created: ${targetInfo.id}`);
    return targetTab;
  }

  /**
   * Get all current targets
   */
  async getTargets() {
    return await listTargets();
  }

  /**
   * Close a target tab
   */
  async closeTargetTab(tabId) {
    const targetTab = this.targetTabs.get(tabId);
    if (targetTab) {
      await targetTab.cdp.close();
      await closeTarget(tabId);
      this.targetTabs.delete(tabId);
      console.log(`🗑️ Closed target tab: ${tabId}`);
    }
  }

  /**
   * Get target tab by ID
   */
  getTargetTab(tabId) {
    return this.targetTabs.get(tabId);
  }

  /**
   * Get all target tabs
   */
  getAllTargetTabs() {
    return Array.from(this.targetTabs.values());
  }

  /**
   * Cleanup and close browser
   */
  async cleanup() {
    console.log("🧹 Cleaning up browser manager...");

    // Close all target tabs
    for (const [tabId] of this.targetTabs) {
      await this.closeTargetTab(tabId);
    }

    // Close control tab
    if (this.controlTab) {
      await this.controlTab.cdp.close();
      await closeTarget(this.controlTab.id);
      this.controlTab = null;
    }

    // Close Puppeteer browser
    if (this.browser) {
      try {
        await this.browser.close();
        this.browser = null;
      } catch (error) {
        console.error("Error closing Puppeteer browser:", error);
      }
    }

    this.isRunning = false;
    console.log("✅ Browser cleanup complete");
  }
}

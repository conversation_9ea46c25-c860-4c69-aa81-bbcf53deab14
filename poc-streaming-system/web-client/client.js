/**
 * POC Browser Streaming Web Client
 *
 * Connects to signaling server and displays multiple streams in grid layout
 * Handles WebRTC connections and stream management
 */

class StreamingClient {
  constructor() {
    this.signalingServerUrl = "ws://localhost:8080";
    this.websocket = null;
    this.isConnected = false;
    this.clientId = null;

    // WebRTC configuration
    this.rtcConfig = {
      iceServers: [
        { urls: "stun:stun.cloudflare.com:3478" },
        { urls: "stun:stun.l.google.com:19302" },
      ],
      iceCandidatePoolSize: 10,
    };

    // State management
    this.availableTabs = new Map(); // tabId -> tabInfo
    this.activeStreams = new Map(); // streamId -> { peerConnection, stream, tabInfo }
    this.pendingStreams = new Map(); // streamId -> tabInfo

    // UI elements
    this.connectBtn = document.getElementById("connectBtn");
    this.disconnectBtn = document.getElementById("disconnectBtn");
    this.statusIndicator = document.getElementById("statusIndicator");
    this.statusText = document.getElementById("statusText");
    this.tabsGrid = document.getElementById("tabsGrid");
    this.streamsGrid = document.getElementById("streamsGrid");
    this.logsContainer = document.getElementById("logsContainer");

    this.init();
  }

  init() {
    this.log("info", "Initializing streaming client...");
    this.setupEventListeners();
    this.updateUI();
  }

  setupEventListeners() {
    this.connectBtn.addEventListener("click", () => this.connect());
    this.disconnectBtn.addEventListener("click", () => this.disconnect());

    // Handle page unload
    window.addEventListener("beforeunload", () => {
      this.disconnect();
    });
  }

  async connect() {
    if (this.isConnected) {
      this.log("warn", "Already connected to signaling server");
      return;
    }

    this.log("info", "Connecting to signaling server...");
    this.updateStatus("connecting", "Connecting...");

    try {
      this.websocket = new WebSocket(this.signalingServerUrl);

      this.websocket.onopen = () => {
        this.log("success", "Connected to signaling server");
        this.isConnected = true;
        this.updateStatus("connected", "Connected");
        this.updateUI();
      };

      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          this.log("error", `Failed to parse message: ${error.message}`);
        }
      };

      this.websocket.onclose = () => {
        this.log("warn", "Disconnected from signaling server");
        this.isConnected = false;
        this.clientId = null;
        this.updateStatus("disconnected", "Disconnected");
        this.updateUI();
        this.clearStreams();
      };

      this.websocket.onerror = (error) => {
        this.log(
          "error",
          `WebSocket error: ${error.message || "Unknown error"}`
        );
        this.updateStatus("error", "Connection Error");
      };
    } catch (error) {
      this.log("error", `Failed to connect: ${error.message}`);
      this.updateStatus("error", "Connection Failed");
    }
  }

  disconnect() {
    if (!this.isConnected) return;

    this.log("info", "Disconnecting from signaling server...");

    // Close all peer connections
    this.clearStreams();

    // Close WebSocket
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }

    this.isConnected = false;
    this.clientId = null;
    this.updateStatus("disconnected", "Disconnected");
    this.updateUI();
  }

  handleMessage(message) {
    this.log("info", `Received: ${message.type}`);

    switch (message.type) {
      case "welcome":
        this.handleWelcome(message);
        break;
      case "available-streams":
        this.handleAvailableStreams(message);
        break;
      case "target-tab-available":
        this.handleTargetTabAvailable(message);
        break;
      case "target-tab-unavailable":
        this.handleTargetTabUnavailable(message);
        break;
      case "target-tab-updated":
        this.handleTargetTabUpdated(message);
        break;
      case "stream-ready":
        this.handleStreamReady(message);
        break;
      case "stream-ended":
        this.handleStreamEnded(message);
        break;
      case "webrtc-offer":
        this.handleWebRTCOffer(message);
        break;
      case "webrtc-answer":
        this.handleWebRTCAnswer(message);
        break;
      case "webrtc-ice-candidate":
        this.handleWebRTCIceCandidate(message);
        break;
      case "error":
        this.log("error", `Server error: ${message.message}`);
        break;
      default:
        this.log("warn", `Unknown message type: ${message.type}`);
    }
  }

  handleWelcome(message) {
    this.clientId = message.clientId;
    this.log("success", `Registered with client ID: ${this.clientId}`);

    // Register as web client
    this.sendMessage({
      type: "register-web-client",
      metadata: {
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
      },
    });
  }

  handleAvailableStreams(message) {
    this.log("info", `Received ${message.targetTabs.length} available tabs`);

    // Update available tabs
    this.availableTabs.clear();
    message.targetTabs.forEach((tab) => {
      this.availableTabs.set(tab.tabId, tab);
    });

    this.updateTabsGrid();
  }

  handleTargetTabAvailable(message) {
    this.log("info", `New tab available: ${message.title}`);
    this.availableTabs.set(message.tabId, message);
    this.updateTabsGrid();
  }

  handleTargetTabUnavailable(message) {
    this.log("warn", `Tab unavailable: ${message.tabId}`);
    this.availableTabs.delete(message.tabId);
    this.updateTabsGrid();

    // Stop any active streams for this tab
    for (const [streamId, stream] of this.activeStreams) {
      if (stream.tabInfo.tabId === message.tabId) {
        this.stopStream(streamId);
      }
    }
  }

  handleTargetTabUpdated(message) {
    this.log("info", `Tab updated: ${message.title}`);
    if (this.availableTabs.has(message.tabId)) {
      const existingTab = this.availableTabs.get(message.tabId);
      this.availableTabs.set(message.tabId, { ...existingTab, ...message });
      this.updateTabsGrid();
    }
  }

  handleStreamReady(message) {
    this.log("success", `Stream ready: ${message.streamId}`);

    // Find the pending stream by tabId and update it with the server's streamId
    const tabId = message.tabId;
    const tabInfo = this.pendingStreams.get(tabId);

    if (tabInfo) {
      // Remove from pending with tabId key and add with streamId key
      this.pendingStreams.delete(tabId);
      this.pendingStreams.set(message.streamId, tabInfo);
      this.log(
        "info",
        `Updated pending stream mapping: ${tabId} -> ${message.streamId}`
      );
    } else {
      this.log("warn", `No pending stream found for tabId: ${tabId}`);
    }
  }

  handleStreamEnded(message) {
    this.log("warn", `Stream ended: ${message.streamId}`);
    this.stopStream(message.streamId);
  }

  async handleWebRTCOffer(message) {
    this.log("info", `Received WebRTC offer for stream: ${message.streamId}`);

    try {
      const streamInfo = this.pendingStreams.get(message.streamId);
      if (!streamInfo) {
        this.log("error", `No pending stream found for: ${message.streamId}`);
        return;
      }

      // Create peer connection
      const peerConnection = new RTCPeerConnection(this.rtcConfig);

      // Setup event handlers
      peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          console.log("candidate");
          this.sendMessage({
            type: "webrtc-ice-candidate",
            candidate: event.candidate,
            targetClientId: message.fromClientId,
            streamId: message.streamId,
          });
        }
      };

      peerConnection.ontrack = (event) => {
        console.group("📺 [WebClient] Track Received Debug");
        this.log("success", `Received media track for: ${message.streamId}`);

        // Enhanced track debugging
        console.log("📡 Track Event Details:", {
          track: {
            kind: event.track.kind,
            id: event.track.id,
            enabled: event.track.enabled,
            muted: event.track.muted,
            readyState: event.track.readyState,
            label: event.track.label,
            settings: event.track.getSettings
              ? event.track.getSettings()
              : "N/A",
          },
          streams: event.streams.map((s) => ({
            id: s.id,
            active: s.active,
            trackCount: s.getTracks().length,
          })),
        });

        const [stream] = event.streams;
        console.log("📊 Received Stream Analysis:", {
          id: stream.id,
          active: stream.active,
          videoTracks: stream.getVideoTracks().length,
          audioTracks: stream.getAudioTracks().length,
          totalTracks: stream.getTracks().length,
        });

        // Debug each track in the stream
        stream.getTracks().forEach((track, index) => {
          console.log(`🎬 Stream Track ${index + 1}:`, {
            kind: track.kind,
            id: track.id,
            enabled: track.enabled,
            muted: track.muted,
            readyState: track.readyState,
            settings: track.getSettings ? track.getSettings() : "N/A",
          });

          // Check for issues
          const issues = [];
          if (track.readyState === "ended") issues.push("Track is ended");
          if (track.muted) issues.push("Track is muted");
          if (!track.enabled) issues.push("Track is disabled");

          if (issues.length > 0) {
            console.error(`❌ Track ${index + 1} issues:`, issues);
          } else {
            console.log(`✅ Track ${index + 1} looks healthy`);
          }
        });

        console.groupEnd();

        // Only display stream once (when we get the first track)
        if (!this.activeStreams.has(message.streamId)) {
          this.displayStream(message.streamId, stream, streamInfo);
        }
      };

      peerConnection.onconnectionstatechange = () => {
        this.log("info", `Connection state: ${peerConnection.connectionState}`);
        if (peerConnection.connectionState === "failed") {
          this.log(
            "error",
            `WebRTC connection failed for stream: ${message.streamId}`
          );
          this.stopStream(message.streamId);
        }
      };

      // Set remote description
      await peerConnection.setRemoteDescription(message.offer);

      // Create answer
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // Send answer
      console.log("sending answer");
      this.sendMessage({
        type: "webrtc-answer",
        answer: answer,
        targetClientId: message.fromClientId,
        streamId: message.streamId,
      });

      // Store the peer connection
      this.activeStreams.set(message.streamId, {
        peerConnection,
        stream: null,
        tabInfo: streamInfo,
      });

      this.pendingStreams.delete(message.streamId);
    } catch (error) {
      this.log("error", `Failed to handle WebRTC offer: ${error.message}`);
    }
  }

  async handleWebRTCAnswer(message) {
    this.log("info", `Received WebRTC answer for stream: ${message.streamId}`);

    const streamData = this.activeStreams.get(message.streamId);
    if (streamData && streamData.peerConnection) {
      try {
        await streamData.peerConnection.setRemoteDescription(message.answer);
      } catch (error) {
        this.log("error", `Failed to set remote description: ${error.message}`);
      }
    }
  }

  async handleWebRTCIceCandidate(message) {
    const streamData = this.activeStreams.get(message.streamId);
    if (streamData && streamData.peerConnection) {
      try {
        await streamData.peerConnection.addIceCandidate(
          new RTCIceCandidate(message.candidate)
        );
      } catch (error) {
        this.log("error", `Failed to add ICE candidate: ${error.message}`);
      }
    }
  }

  startStream(tabId) {
    const tabInfo = this.availableTabs.get(tabId);
    if (!tabInfo) {
      this.log("error", `Tab not found: ${tabId}`);
      return;
    }

    this.log("info", `Starting stream for tab: ${tabInfo.title}`);

    // Store tab info with tabId as key for now, will update with streamId when server responds
    this.pendingStreams.set(tabId, tabInfo);

    this.sendMessage({
      type: "start-stream",
      tabId: tabId,
    });
  }

  stopStream(streamId) {
    this.log("info", `Stopping stream: ${streamId}`);

    const streamData = this.activeStreams.get(streamId);
    if (streamData) {
      // Close peer connection
      if (streamData.peerConnection) {
        streamData.peerConnection.close();
      }

      // Remove from active streams
      this.activeStreams.delete(streamId);

      // Remove from UI
      this.removeStreamFromUI(streamId);
    }

    // Send stop message
    this.sendMessage({
      type: "stop-stream",
      streamId: streamId,
    });
  }

  displayStream(streamId, mediaStream, tabInfo) {
    console.group(`📺 [WebClient] Display Stream Debug - ${streamId}`);
    this.log("success", `Displaying stream: ${streamId}`);

    // Enhanced stream debugging
    if (!mediaStream) {
      console.error("❌ MediaStream is null/undefined");
      console.groupEnd();
      return;
    }

    console.log("📊 Display Stream Info:", {
      streamId: streamId,
      streamActive: mediaStream.active,
      streamId_actual: mediaStream.id,
      trackCount: mediaStream.getTracks().length,
      videoTracks: mediaStream.getVideoTracks().length,
      audioTracks: mediaStream.getAudioTracks().length,
      tabInfo: tabInfo,
    });

    // Debug each track before display
    mediaStream.getTracks().forEach((track, index) => {
      console.log(`🎬 Display Track ${index + 1}:`, {
        kind: track.kind,
        id: track.id,
        enabled: track.enabled,
        muted: track.muted,
        readyState: track.readyState,
        settings: track.getSettings ? track.getSettings() : "N/A",
      });

      // Check for display-blocking issues
      const issues = [];
      if (track.readyState === "ended") issues.push("Track is ended");
      if (track.muted) issues.push("Track is muted");
      if (!track.enabled) issues.push("Track is disabled");

      if (issues.length > 0) {
        console.error(`❌ Display Track ${index + 1} issues:`, issues);
      }
    });

    // Update stream data
    const streamData = this.activeStreams.get(streamId);
    if (streamData) {
      streamData.stream = mediaStream;
    }

    // Only create UI if it doesn't already exist
    const existingContainer = document.getElementById(`stream-${streamId}`);
    if (!existingContainer) {
      console.log("🎮 Creating new video UI element");
      this.addStreamToUI(streamId, mediaStream, tabInfo);
    } else {
      console.log("🎮 Updating existing video element");
      // Update existing video element with new stream
      const video = existingContainer.querySelector(".stream-video");
      if (video) {
        this.debugVideoElementAssignment(video, mediaStream, "update");
        video.srcObject = mediaStream;
      } else {
        console.error("❌ Video element not found in existing container");
      }
    }

    console.groupEnd();
  }

  addStreamToUI(streamId, mediaStream, tabInfo) {
    // Remove empty state if present
    const emptyState = this.streamsGrid.querySelector(".empty-state");
    if (emptyState) {
      emptyState.remove();
    }

    const streamContainer = document.createElement("div");
    streamContainer.className = "stream-container";
    streamContainer.id = `stream-${streamId}`;

    streamContainer.innerHTML = `
      <div class="stream-header">
        📺 ${tabInfo.title || "Unknown Tab"}
      </div>
      <div class="stream-content">
        <video class="stream-video" autoplay muted playsinline></video>
      </div>
      <div class="stream-controls">
        <div class="stream-info">
          <div>${tabInfo.url || "Unknown URL"}</div>
          <div>Stream ID: ${streamId.substring(0, 8)}...</div>
        </div>
        <button class="btn btn-secondary btn-small" onclick="client.stopStream('${streamId}')">
          Stop Stream
        </button>
      </div>
    `;

    const video = streamContainer.querySelector(".stream-video");
    console.log("🎮 [WebClient] Setting up new video element");

    // Debug video element assignment
    this.debugVideoElementAssignment(video, mediaStream, "new");
    video.srcObject = mediaStream;
    video.onerror = (error) => {
      console.error("[WebClient] Video error:", error);
    };

    this.streamsGrid.appendChild(streamContainer);
  }

  removeStreamFromUI(streamId) {
    const streamElement = document.getElementById(`stream-${streamId}`);
    if (streamElement) {
      streamElement.remove();
    }

    // Add empty state if no streams left
    if (this.streamsGrid.children.length === 0) {
      this.streamsGrid.innerHTML = `
        <div class="empty-state">
          <h3>No active streams</h3>
          <p>Click "Start Stream" on any available tab to begin streaming</p>
        </div>
      `;
    }
  }

  debugVideoElementAssignment(videoElement, mediaStream, context = "unknown") {
    console.group(`🎮 [WebClient] Video Element Assignment Debug - ${context}`);

    if (!videoElement) {
      console.error("❌ Video element is null/undefined");
      console.groupEnd();
      return;
    }

    if (!mediaStream) {
      console.error("❌ MediaStream is null/undefined");
      console.groupEnd();
      return;
    }

    console.log("🎮 Video Element Info:", {
      tagName: videoElement.tagName,
      autoplay: videoElement.autoplay,
      muted: videoElement.muted,
      playsinline: videoElement.playsinline,
      controls: videoElement.controls,
      currentSrcObject: videoElement.srcObject?.id || "none",
      readyState: videoElement.readyState,
      networkState: videoElement.networkState,
      paused: videoElement.paused,
      ended: videoElement.ended,
      currentTime: videoElement.currentTime,
      duration: videoElement.duration,
      videoWidth: videoElement.videoWidth,
      videoHeight: videoElement.videoHeight,
    });

    console.log("📊 MediaStream Info:", {
      id: mediaStream.id,
      active: mediaStream.active,
      trackCount: mediaStream.getTracks().length,
    });

    // Enhanced event listeners for debugging
    const originalOnLoadedMetadata = videoElement.onloadedmetadata;
    videoElement.onloadedmetadata = (event) => {
      console.log("🎮 [Video Debug] Metadata loaded:", {
        width: videoElement.videoWidth,
        height: videoElement.videoHeight,
        duration: videoElement.duration,
      });

      if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
        console.error(
          "❌ [Video Debug] ZERO DIMENSIONS - BLACK SCREEN DETECTED!"
        );
        console.error(
          "❌ This indicates the video track has no content or is not properly captured"
        );
      } else {
        console.log("✅ [Video Debug] Valid video dimensions detected");
      }

      if (originalOnLoadedMetadata)
        originalOnLoadedMetadata.call(videoElement, event);
    };

    const originalOnPlay = videoElement.onplay;
    videoElement.onplay = (event) => {
      console.log("🎮 [Video Debug] Video started playing");
      if (originalOnPlay) originalOnPlay.call(videoElement, event);
    };

    const originalOnError = videoElement.onerror;
    videoElement.onerror = (event) => {
      console.error("❌ [Video Debug] Video error:", {
        error: videoElement.error,
        networkState: videoElement.networkState,
        readyState: videoElement.readyState,
      });
      if (originalOnError) originalOnError.call(videoElement, event);
    };

    videoElement.onstalled = () => {
      console.warn("⚠️ [Video Debug] Video stalled");
    };

    videoElement.onwaiting = () => {
      console.warn("⚠️ [Video Debug] Video waiting for data");
    };

    // Monitor video element state periodically
    const monitorInterval = setInterval(() => {
      if (!videoElement.parentNode) {
        clearInterval(monitorInterval);
        return;
      }

      // Only log if there are issues
      if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
        console.warn("⚠️ [Video Debug] Video dimensions still 0x0:", {
          readyState: videoElement.readyState,
          networkState: videoElement.networkState,
          paused: videoElement.paused,
          currentTime: videoElement.currentTime,
        });
      }
    }, 3000);

    // Clear monitor after 30 seconds
    setTimeout(() => clearInterval(monitorInterval), 30000);

    console.groupEnd();
  }

  updateTabsGrid() {
    if (this.availableTabs.size === 0) {
      this.tabsGrid.innerHTML = `
        <div class="empty-state">
          <h3>No target tabs available</h3>
          <p>Connect to the signaling server to see available tabs</p>
        </div>
      `;
      return;
    }

    this.tabsGrid.innerHTML = "";

    for (const [tabId, tabInfo] of this.availableTabs) {
      const isStreaming = Array.from(this.activeStreams.values()).some(
        (stream) => stream.tabInfo.tabId === tabId
      );

      const tabCard = document.createElement("div");
      tabCard.className = `tab-card ${isStreaming ? "streaming" : ""}`;

      tabCard.innerHTML = `
        <div class="tab-title">${tabInfo.title || "Unknown Tab"}</div>
        <div class="tab-url">${tabInfo.url || "Unknown URL"}</div>
        <div class="tab-actions">
          <button class="btn btn-primary btn-small" 
                  onclick="client.startStream('${tabId}')"
                  ${isStreaming ? "disabled" : ""}>
            ${isStreaming ? "Streaming" : "Start Stream"}
          </button>
        </div>
      `;

      this.tabsGrid.appendChild(tabCard);
    }
  }

  clearStreams() {
    // Close all peer connections
    for (const [, streamData] of this.activeStreams) {
      if (streamData.peerConnection) {
        streamData.peerConnection.close();
      }
    }

    this.activeStreams.clear();
    this.pendingStreams.clear();

    // Clear UI
    this.streamsGrid.innerHTML = `
      <div class="empty-state">
        <h3>No active streams</h3>
        <p>Click "Start Stream" on any available tab to begin streaming</p>
      </div>
    `;
  }

  updateStatus(status, text) {
    this.statusText.textContent = text;
    this.statusIndicator.className = `status-indicator ${
      status === "connected" ? "connected" : ""
    }`;
  }

  updateUI() {
    this.connectBtn.disabled = this.isConnected;
    this.disconnectBtn.disabled = !this.isConnected;
  }

  sendMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message));
    } else {
      this.log("warn", "Cannot send message - not connected");
    }
  }

  log(level, message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement("div");
    logEntry.className = "log-entry";
    logEntry.innerHTML = `
      <span class="log-timestamp">[${timestamp}]</span>
      <span class="log-level-${level}">[${level.toUpperCase()}]</span>
      ${message}
    `;

    this.logsContainer.appendChild(logEntry);
    this.logsContainer.scrollTop = this.logsContainer.scrollHeight;

    // Keep only last 100 log entries
    while (this.logsContainer.children.length > 100) {
      this.logsContainer.removeChild(this.logsContainer.firstChild);
    }

    console.log(`[${level.toUpperCase()}] ${message}`);
  }

  generateId() {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }
}

// Initialize the client
const client = new StreamingClient();

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POC Browser Streaming Client</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            color: #4a5568;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .controls {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: #4299e1;
            color: white;
        }

        .btn-primary:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e53e3e;
            animation: pulse 2s infinite;
        }

        .status-indicator.connected {
            background: #38a169;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .main-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .streams-section {
            margin-bottom: 2rem;
        }

        .section-title {
            color: white;
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .available-tabs {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .tabs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .tab-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .tab-card:hover {
            border-color: #4299e1;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.15);
        }

        .tab-card.streaming {
            border-color: #38a169;
            background: #f0fff4;
        }

        .tab-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .tab-url {
            color: #718096;
            font-size: 0.8rem;
            word-break: break-all;
            margin-bottom: 0.5rem;
        }

        .tab-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.75rem;
        }

        .btn-small {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }

        .streams-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .stream-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stream-container:hover {
            transform: translateY(-4px);
        }

        .stream-header {
            background: #4a5568;
            color: white;
            padding: 1rem;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .stream-content {
            position: relative;
            background: #000;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stream-video {
            width: 100%;
            height: auto;
            max-height: 400px;
            object-fit: contain;
        }

        .stream-placeholder {
            color: #a0aec0;
            font-size: 1rem;
            text-align: center;
        }

        .stream-controls {
            padding: 1rem;
            background: #f7fafc;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stream-info {
            font-size: 0.8rem;
            color: #718096;
        }

        .logs-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .logs-container {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 0.25rem;
        }

        .log-timestamp {
            color: #a0aec0;
            margin-right: 0.5rem;
        }

        .log-level-info { color: #63b3ed; }
        .log-level-warn { color: #f6e05e; }
        .log-level-error { color: #fc8181; }
        .log-level-success { color: #68d391; }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .empty-state h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .empty-state p {
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .main-content {
                padding: 1rem;
            }

            .streams-grid {
                grid-template-columns: 1fr;
            }

            .tabs-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎥 POC Browser Streaming Client</h1>
        <div class="controls">
            <button id="connectBtn" class="btn btn-primary">Connect to Server</button>
            <button id="disconnectBtn" class="btn btn-secondary" disabled>Disconnect</button>
            <div class="status">
                <div id="statusIndicator" class="status-indicator"></div>
                <span id="statusText">Disconnected</span>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="available-tabs">
            <h2 class="section-title" style="color: #4a5568; text-shadow: none;">📑 Available Target Tabs</h2>
            <div id="tabsGrid" class="tabs-grid">
                <div class="empty-state">
                    <h3>No target tabs available</h3>
                    <p>Connect to the signaling server to see available tabs</p>
                </div>
            </div>
        </div>

        <div class="streams-section">
            <h2 class="section-title">🎬 Active Streams</h2>
            <div id="streamsGrid" class="streams-grid">
                <div class="empty-state">
                    <h3>No active streams</h3>
                    <p>Click "Start Stream" on any available tab to begin streaming</p>
                </div>
            </div>
        </div>

        <div class="logs-section">
            <h3 style="color: #4a5568; margin-bottom: 1rem;">📋 Activity Logs</h3>
            <div id="logsContainer" class="logs-container"></div>
        </div>
    </div>

    <script src="client.js"></script>
</body>
</html>

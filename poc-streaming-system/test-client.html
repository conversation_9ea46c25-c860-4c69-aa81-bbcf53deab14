<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POC Streaming System - Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.connected {
            background-color: #4CAF50;
            color: white;
        }
        
        .status.disconnected {
            background-color: #f44336;
            color: white;
        }
        
        button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background-color: #1976D2;
        }
        
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .tab-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        
        .tab-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .tab-url {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        
        .logs {
            background-color: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
        }
        
        .timestamp {
            color: #888;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 POC Streaming System - Test Client</h1>
        <p>Test the browser streaming functionality</p>
    </div>

    <div class="section">
        <h2>Connection Status</h2>
        <button id="connectBtn">Connect to Signaling Server</button>
        <span id="connectionStatus" class="status disconnected">Disconnected</span>
        
        <div style="margin-top: 15px;">
            <strong>Signaling Server:</strong> ws://localhost:8080<br>
            <strong>Web Server:</strong> http://localhost:3000<br>
            <strong>Browser CDP:</strong> http://localhost:9222
        </div>
    </div>

    <div class="grid">
        <div class="section">
            <h2>Available Target Tabs</h2>
            <button id="refreshTabsBtn" disabled>Refresh Tabs</button>
            <div id="targetTabs">
                <p>Connect to server to see available tabs...</p>
            </div>
        </div>

        <div class="section">
            <h2>System Status</h2>
            <button id="refreshStatusBtn" disabled>Refresh Status</button>
            <div id="systemStatus">
                <p>Connect to server to see system status...</p>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Activity Logs</h2>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="logs" class="logs"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        const connectBtn = document.getElementById('connectBtn');
        const connectionStatus = document.getElementById('connectionStatus');
        const refreshTabsBtn = document.getElementById('refreshTabsBtn');
        const refreshStatusBtn = document.getElementById('refreshStatusBtn');
        const targetTabsDiv = document.getElementById('targetTabs');
        const systemStatusDiv = document.getElementById('systemStatus');
        const logsDiv = document.getElementById('logs');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function clearLogs() {
            logsDiv.innerHTML = '';
        }

        function updateConnectionStatus(connected) {
            isConnected = connected;
            connectionStatus.textContent = connected ? 'Connected' : 'Disconnected';
            connectionStatus.className = `status ${connected ? 'connected' : 'disconnected'}`;
            connectBtn.textContent = connected ? 'Disconnect' : 'Connect to Signaling Server';
            refreshTabsBtn.disabled = !connected;
            refreshStatusBtn.disabled = !connected;
        }

        function connectToSignalingServer() {
            if (ws) {
                ws.close();
                return;
            }

            log('Connecting to signaling server...');
            ws = new WebSocket('ws://localhost:8080');

            ws.onopen = () => {
                log('✅ Connected to signaling server');
                updateConnectionStatus(true);
                
                // Register as web client
                ws.send(JSON.stringify({
                    type: 'register-web-client',
                    metadata: { testClient: true }
                }));
            };

            ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                } catch (error) {
                    log(`❌ Failed to parse message: ${error.message}`);
                }
            };

            ws.onclose = () => {
                log('🔌 Disconnected from signaling server');
                updateConnectionStatus(false);
                ws = null;
            };

            ws.onerror = (error) => {
                log(`❌ WebSocket error: ${error}`);
            };
        }

        function handleMessage(message) {
            log(`📨 Received: ${message.type}`);

            switch (message.type) {
                case 'welcome':
                    log(`🎉 Welcome! Client ID: ${message.clientId}`);
                    refreshTabs();
                    refreshStatus();
                    break;
                    
                case 'available-streams':
                    displayTargetTabs(message.targetTabs || []);
                    break;
                    
                case 'stream-ready':
                    log(`🎥 Stream ready: ${message.streamId}`);
                    break;
                    
                case 'webrtc-offer':
                    log(`📞 WebRTC offer received from: ${message.fromClientId}`);
                    // In a real implementation, we would handle the WebRTC offer
                    break;
                    
                default:
                    log(`📋 Message: ${JSON.stringify(message)}`);
            }
        }

        function displayTargetTabs(tabs) {
            if (tabs.length === 0) {
                targetTabsDiv.innerHTML = '<p>No target tabs available</p>';
                return;
            }

            targetTabsDiv.innerHTML = tabs.map(tab => `
                <div class="tab-item">
                    <h4>${tab.title || 'Loading...'}</h4>
                    <div class="tab-url">${tab.url}</div>
                    <div>
                        <strong>ID:</strong> ${tab.id}<br>
                        <strong>Status:</strong> ${tab.isInjected ? '✅ Script Injected' : '⏳ Loading'}
                    </div>
                    <button onclick="startStream('${tab.id}')" ${!tab.isInjected ? 'disabled' : ''}>
                        Start Stream
                    </button>
                </div>
            `).join('');
        }

        function startStream(tabId) {
            if (!ws || !isConnected) {
                log('❌ Not connected to signaling server');
                return;
            }

            log(`🎬 Starting stream for tab: ${tabId}`);
            ws.send(JSON.stringify({
                type: 'start-stream',
                targetTabId: tabId
            }));
        }

        async function refreshTabs() {
            if (!isConnected) return;
            
            log('🔄 Refreshing target tabs...');
            ws.send(JSON.stringify({
                type: 'get-available-streams'
            }));
        }

        async function refreshStatus() {
            if (!isConnected) return;
            
            try {
                const response = await fetch('/api/status');
                const status = await response.json();
                
                systemStatusDiv.innerHTML = `
                    <div><strong>System Running:</strong> ${status.isRunning ? '✅ Yes' : '❌ No'}</div>
                    <div><strong>Target Tabs:</strong> ${status.targetTabs.length}</div>
                    <div><strong>Signaling Clients:</strong> ${status.stats.totalClients}</div>
                    <div><strong>Active Streams:</strong> ${status.stats.activeStreams}</div>
                    <div><strong>Control Tab:</strong> ${status.stats.hasControlTab ? '✅ Connected' : '❌ Not Connected'}</div>
                `;
                
                log('📊 System status updated');
            } catch (error) {
                log(`❌ Failed to fetch system status: ${error.message}`);
                systemStatusDiv.innerHTML = '<p>Failed to fetch system status</p>';
            }
        }

        // Event listeners
        connectBtn.addEventListener('click', connectToSignalingServer);
        refreshTabsBtn.addEventListener('click', refreshTabs);
        refreshStatusBtn.addEventListener('click', refreshStatus);

        // Initial log
        log('🚀 Test client initialized');
        log('Click "Connect to Signaling Server" to begin testing');
    </script>
</body>
</html>

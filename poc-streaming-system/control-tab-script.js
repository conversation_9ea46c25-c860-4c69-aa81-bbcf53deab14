/**
 * Control Tab Script for POC Streaming
 *
 * This script is injected into the control tab to manage WebRTC connections
 * and coordinate streaming between target tabs and web clients
 */

(function () {
  "use strict";

  console.log("[POC-Streaming] Control tab script initializing...");

  // Prevent multiple injections
  if (window.pocControlTabInjected) {
    console.log(
      "[POC-Streaming] Control tab script already injected, skipping..."
    );
    return;
  }
  window.pocControlTabInjected = true;

  class ControlTabManager {
    constructor() {
      this.signalingServerUrl = "ws://localhost:8080"; // Will be set dynamically
      this.websocket = null;
      this.isConnected = false;
      this.clientId = null;

      // WebRTC configuration
      this.rtcConfig = {
        iceServers: [
          {
            urls: "stun:stun.cloudflare.com:3478",
          },
          {
            urls: "turn:relay1.expressturn.com:3478",
            username: "ef89RMU4SHUQMSOUU9",
            credential: "jvkMMnQxWX4Qrhe3",
          },
        ],
        iceCandidatePoolSize: 10,
      };

      // Connection management
      this.peerConnections = new Map(); // streamId -> RTCPeerConnection (to web client)
      this.targetConnections = new Map(); // streamId -> RTCPeerConnection (to target tab)
      this.targetTabs = new Map(); // tabId -> tabInfo
      this.pendingStreams = new Map(); // streamId -> { targetTabId, requestedBy, status }
      this.activeStreams = new Map(); // streamId -> { targetTabId, webClientId, peerConnection }

      this.init();
    }

    async init() {
      console.log("[POC-Streaming] Initializing control tab manager...");
      await this.connectToSignalingServer();
      this.setupPageListeners();
    }

    async connectToSignalingServer() {
      try {
        console.log("[POC-Streaming] Connecting to signaling server...");
        this.websocket = new WebSocket(this.signalingServerUrl);

        this.websocket.onopen = () => {
          console.log(
            "[POC-Streaming] Control tab connected to signaling server"
          );
          this.isConnected = true;

          // Register as control tab
          this.sendMessage({
            type: "register-control-tab",
            metadata: {
              userAgent: navigator.userAgent,
              timestamp: Date.now(),
            },
          });
        };

        this.websocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error("[POC-Streaming] Failed to parse message:", error);
          }
        };

        this.websocket.onclose = () => {
          console.log(
            "[POC-Streaming] Control tab disconnected from signaling server"
          );
          this.isConnected = false;
          this.scheduleReconnect();
        };

        this.websocket.onerror = (error) => {
          console.error("[POC-Streaming] Control tab WebSocket error:", error);
        };
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to connect to signaling server:",
          error
        );
        this.scheduleReconnect();
      }
    }

    scheduleReconnect() {
      console.log("[POC-Streaming] Scheduling reconnection in 5 seconds...");
      setTimeout(() => {
        this.connectToSignalingServer();
      }, 5000);
    }

    sendMessage(message) {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify(message));
      } else {
        console.warn("[POC-Streaming] Cannot send message - not connected");
      }
    }

    handleMessage(message) {
      console.log(
        "[POC-Streaming] Control tab received message:",
        message.type
      );

      switch (message.type) {
        case "welcome":
          this.handleWelcome(message);
          break;
        case "target-tabs-list":
          this.handleTargetTabsList(message);
          break;
        case "target-tab-registered":
          this.handleTargetTabRegistered(message);
          break;
        case "target-tab-disconnected":
          this.handleTargetTabDisconnected(message);
          break;
        case "stream-requested":
          // this.handleStreamRequested(message);
          break;
        case "stream-stopped":
          this.handleStreamStopped(message);
          break;
        case "webrtc-offer":
          this.handleWebRTCOffer(message);
          break;
        case "webrtc-answer":
          this.handleWebRTCAnswer(message);
          break;
        case "webrtc-ice-candidate":
          this.handleWebRTCIceCandidate(message);
          break;
        case "webrtc-offer-from-target":
          this.handleTargetTabOffer(message);
          break;
        case "webrtc-ice-candidate-from-target":
          this.handleTargetTabIceCandidate(message);
          break;
        default:
          console.log("[POC-Streaming] Unknown message type:", message.type);
      }
    }

    handleWelcome(message) {
      this.clientId = message.clientId;
      console.log(
        "[POC-Streaming] Control tab registered with ID:",
        this.clientId
      );
    }

    handleTargetTabsList(message) {
      console.log(
        "[POC-Streaming] Received target tabs list:",
        message.targetTabs.length
      );

      // Update target tabs
      this.targetTabs.clear();
      message.targetTabs.forEach((tab) => {
        this.targetTabs.set(tab.tabId, tab);
      });
    }

    handleTargetTabRegistered(message) {
      console.log("[POC-Streaming] Target tab registered:", message.tabId);
      this.targetTabs.set(message.tabId, message);
    }

    handleTargetTabDisconnected(message) {
      console.log("[POC-Streaming] Target tab disconnected:", message.tabId);
      this.targetTabs.delete(message.tabId);

      // Clean up any active streams for this tab
      for (const [streamId, stream] of this.activeStreams) {
        if (stream.targetTabId === message.tabId) {
          this.cleanupStream(streamId);
        }
      }
    }

    handleStreamStopped(message) {
      console.log("[POC-Streaming] Stream stopped:", message.streamId);
      this.cleanupStream(message.streamId);
    }

    async handleWebRTCOffer() {
      console.log("[POC-Streaming] Received WebRTC offer");
      // This would be handled if control tab receives offers (not typical in this architecture)
    }

    async handleWebRTCAnswer(message) {
      console.log(
        "[POC-Streaming] Received WebRTC answer for stream:",
        message.streamId
      );

      const peerConnection = this.peerConnections.get(message.streamId);
      if (peerConnection) {
        try {
          await peerConnection.setRemoteDescription(
            new RTCSessionDescription(message.answer)
          );
          console.log(
            "[POC-Streaming] WebRTC connection established for stream:",
            message.streamId
          );
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to set remote description:",
            error
          );
        }
      }
    }

    async handleWebRTCIceCandidate(message) {
      const peerConnection = this.peerConnections.get(message.streamId);
      if (peerConnection) {
        try {
          await peerConnection.addIceCandidate(message.candidate);
        } catch (error) {
          console.error("[POC-Streaming] Failed to add ICE candidate:", error);
        }
      }
    }

    async handleTargetTabOffer(message) {
      console.log(
        "[POC-Streaming] Received WebRTC offer from target tab:",
        message.streamId
      );
      console.log("[POC-Streaming] Target tab message:", message);

      // Create stream info from the message since target tab is initiating
      const streamInfo = {
        targetTabId: message.targetTabId,
        requestedBy: message.targetClientId,
        status: "connecting",
      };

      console.log("[POC-Streaming] Stream info created:", streamInfo);

      // Store the stream info
      this.pendingStreams.set(message.streamId, streamInfo);

      // Create peer connection to target tab
      const targetPeerConnection = new RTCPeerConnection(this.rtcConfig);

      // Create peer connection to web client
      const clientPeerConnection = new RTCPeerConnection(this.rtcConfig);

      // Store both connections
      this.targetConnections.set(message.streamId, targetPeerConnection);
      this.peerConnections.set(message.streamId, clientPeerConnection);

      // Handle incoming stream from target tab
      targetPeerConnection.ontrack = (event) => {
        console.group("🔄 [POC-Streaming] Control Tab Relay Debug");
        console.log("[POC-Streaming] Received stream from target tab");

        // Enhanced event debugging
        console.log("📡 Track Event Details:", {
          track: {
            kind: event.track.kind,
            id: event.track.id,
            enabled: event.track.enabled,
            muted: event.track.muted,
            readyState: event.track.readyState,
            label: event.track.label,
          },
          streams: event.streams.map((s) => ({
            id: s.id,
            active: s.active,
            trackCount: s.getTracks().length,
          })),
        });

        const [stream] = event.streams;
        console.log("📊 Stream Analysis:", {
          id: stream.id,
          active: stream.active,
          videoTracks: stream.getVideoTracks().length,
          audioTracks: stream.getAudioTracks().length,
          totalTracks: stream.getTracks().length,
        });

        // Debug each track before forwarding
        stream.getTracks().forEach((track, index) => {
          const newMediaSteam = new MediaStream([track]);
          console.log(`🎬 Track ${index + 1} Relay:`, {
            kind: track.kind,
            id: track.id,
            enabled: track.enabled,
            muted: track.muted,
            readyState: track.readyState,
            settings: track.getSettings ? track.getSettings() : "N/A",
          });

          // Check for issues before forwarding
          const issues = [];
          if (track.readyState === "ended") issues.push("Track is ended");
          if (track.muted) issues.push("Track is muted");
          if (!track.enabled) issues.push("Track is disabled");

          if (issues.length > 0) {
            console.error(`❌ Track ${index + 1} issues before relay:`, issues);

            // If track is muted, try to wait for it to unmute
            if (track.muted) {
              console.log(`⏳ Waiting for track ${track.id} to unmute...`);

              // Set up unmute listener
              const unmutedHandler = () => {
                console.log(
                  `✅ Track ${track.id} unmuted! Attempting to refresh relay...`
                );
                track.removeEventListener("unmute", unmutedHandler);

                // Try to refresh the peer connection with the unmuted track
                // Remove and re-add the track
                const senders = clientPeerConnection.getSenders();
                const existingSender = senders.find(
                  (s) => s.track && s.track.id === track.id
                );
                if (existingSender) {
                  existingSender
                    .replaceTrack(track)
                    .then(() => {
                      console.log(`✅ Track ${track.id} replaced successfully`);
                    })
                    .catch((err) => {
                      console.error(
                        `❌ Failed to replace track ${track.id}:`,
                        err
                      );
                    });
                }
              };

              track.addEventListener("unmute", unmutedHandler);

              // Also check periodically for unmute (backup)
              const checkUnmute = setInterval(() => {
                if (!track.muted) {
                  clearInterval(checkUnmute);
                  track.removeEventListener("unmute", unmutedHandler);
                  console.log(
                    `✅ Track ${track.id} detected as unmuted via polling`
                  );
                }
              }, 500);

              // Stop checking after 10 seconds
              setTimeout(() => {
                clearInterval(checkUnmute);
                track.removeEventListener("unmute", unmutedHandler);
                if (track.muted) {
                  console.error(
                    `❌ Track ${track.id} remained muted after 10 seconds`
                  );
                }
              }, 10000);
            }
          }

          // Add track to client peer connection
          console.log(`🔄 Adding track ${track.id} to client peer connection`);
          clientPeerConnection.addTrack(
            newMediaSteam.getTracks()[0],
            newMediaSteam
          );
          this.createControlTabDebugVideo(newMediaSteam, message.streamId);
        });

        // Verify tracks were added to client peer connection
        setTimeout(() => {
          const clientSenders = clientPeerConnection.getSenders();
          console.log("✅ Client Peer Connection Verification:", {
            sendersCount: clientSenders.length,
            senders: clientSenders.map((sender) => ({
              trackId: sender.track?.id,
              trackKind: sender.track?.kind,
              trackEnabled: sender.track?.enabled,
              trackReadyState: sender.track?.readyState,
            })),
          });

          // Check if all tracks were properly added
          const originalTrackIds = stream.getTracks().map((t) => t.id);
          const forwardedTrackIds = clientSenders
            .map((s) => s.track?.id)
            .filter(Boolean);

          if (originalTrackIds.length !== forwardedTrackIds.length) {
            console.error("❌ Track count mismatch!", {
              original: originalTrackIds.length,
              forwarded: forwardedTrackIds.length,
            });
          } else {
            console.log("✅ All tracks successfully forwarded");
          }
        }, 100);

        // Create debug video element to verify stream content in control tab

        console.groupEnd();

        // Create offer to web client
        this.createOfferToWebClient(message.streamId, streamInfo.requestedBy);
      };

      // Handle ICE candidates from target tab
      targetPeerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          // Send ICE candidate back to target tab
          this.sendMessage({
            type: "webrtc-ice-candidate-to-target",
            candidate: event.candidate,
            streamId: message.streamId,
            targetTabId: streamInfo.targetTabId,
          });
        }
      };

      // Handle ICE candidates from web client
      clientPeerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendMessage({
            type: "webrtc-ice-candidate",
            candidate: event.candidate,
            streamId: message.streamId,
            targetClientId: streamInfo.requestedBy,
          });
        }
      };

      // Accept the offer from target tab
      try {
        await targetPeerConnection.setRemoteDescription(
          new RTCSessionDescription(message.offer)
        );
        const answer = await targetPeerConnection.createAnswer();
        await targetPeerConnection.setLocalDescription(answer);

        // Send answer back to target tab
        this.sendMessage({
          type: "webrtc-answer-to-target",
          answer: answer,
          streamId: message.streamId,
          targetTabId: streamInfo.targetTabId,
        });

        console.log("[POC-Streaming] WebRTC answer sent to target tab");
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to handle target tab offer:",
          error
        );
      }
    }

    async handleTargetTabIceCandidate(message) {
      const targetPeerConnection = this.targetConnections.get(message.streamId);
      if (targetPeerConnection) {
        try {
          await targetPeerConnection.addIceCandidate(message.candidate);
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to add target ICE candidate:",
            error
          );
        }
      }
    }

    async createOfferToWebClient(streamId, webClientId) {
      const peerConnection = this.peerConnections.get(streamId);
      if (!peerConnection) {
        console.error(
          "[POC-Streaming] No peer connection found for stream:",
          streamId
        );
        return;
      }

      try {
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);

        // Send offer to web client
        this.sendMessage({
          type: "webrtc-offer",
          offer: offer,
          targetClientId: webClientId,
          streamId: streamId,
          fromClientId: this.clientId,
        });

        console.log(
          "[POC-Streaming] WebRTC offer sent to web client for stream:",
          streamId
        );
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to create offer to web client:",
          error
        );
      }
    }

    createControlTabDebugVideo(stream, streamId) {
      console.group("🎮 [Control Tab] Creating Debug Video");

      try {
        // Remove any existing debug video for this stream
        const existingDebugVideo = document.getElementById(
          `control-debug-${streamId}`
        );
        if (existingDebugVideo && existingDebugVideo.parentNode) {
          existingDebugVideo.parentNode.removeChild(existingDebugVideo);
        }

        // Create debug video container
        const container = document.createElement("div");
        container.id = `control-debug-container-${streamId}`;
        container.style.position = "fixed";
        container.style.top = "10px";
        container.style.left = "10px";
        container.style.zIndex = "999999";
        container.style.background = "rgba(0,0,0,0.8)";
        container.style.padding = "10px";
        container.style.borderRadius = "5px";
        container.style.border = "3px solid #ff6600";

        // Create debug video element
        const debugVideo = document.createElement("video");
        debugVideo.id = `control-debug-${streamId}`;
        debugVideo.srcObject = stream;
        debugVideo.autoplay = true;
        debugVideo.muted = true;
        debugVideo.playsinline = true;
        debugVideo.style.width = "300px";
        debugVideo.style.height = "200px";
        debugVideo.style.display = "block";

        // Create info overlay
        const overlay = document.createElement("div");
        overlay.style.position = "absolute";
        overlay.style.top = "5px";
        overlay.style.left = "5px";
        overlay.style.background = "rgba(255,102,0,0.9)";
        overlay.style.color = "white";
        overlay.style.padding = "5px";
        overlay.style.fontSize = "12px";
        overlay.style.fontFamily = "monospace";
        overlay.style.borderRadius = "3px";
        overlay.textContent = `CONTROL TAB DEBUG - ${streamId.substring(0, 8)}`;

        // Create status indicator
        const statusDiv = document.createElement("div");
        statusDiv.style.color = "white";
        statusDiv.style.fontSize = "10px";
        statusDiv.style.marginTop = "5px";
        statusDiv.style.fontFamily = "monospace";
        statusDiv.textContent = "Loading...";

        // Enhanced event listeners
        debugVideo.onloadstart = () => {
          console.log("🎮 [Control Debug] Video load started");
          statusDiv.textContent = "Load started...";
        };

        debugVideo.onloadedmetadata = () => {
          const dimensions = {
            width: debugVideo.videoWidth,
            height: debugVideo.videoHeight,
            duration: debugVideo.duration,
          };
          console.log("🎮 [Control Debug] Metadata loaded:", dimensions);

          if (dimensions.width === 0 || dimensions.height === 0) {
            console.error(
              "❌ [Control Debug] ZERO DIMENSIONS - Stream has no content!"
            );
            overlay.style.background = "rgba(255,0,0,0.9)";
            overlay.textContent = `CONTROL TAB - NO CONTENT! - ${streamId.substring(
              0,
              8
            )}`;
            statusDiv.textContent = `❌ 0x0 dimensions - No video content!`;
            statusDiv.style.color = "#ff4444";
          } else {
            console.log(
              "✅ [Control Debug] Valid dimensions - Stream has content!"
            );
            overlay.style.background = "rgba(0,255,0,0.9)";
            overlay.textContent = `CONTROL TAB - CONTENT OK! - ${streamId.substring(
              0,
              8
            )}`;
            statusDiv.textContent = `✅ ${dimensions.width}x${dimensions.height} - Content received!`;
            statusDiv.style.color = "#44ff44";
          }
        };

        debugVideo.oncanplay = () => {
          console.log("🎮 [Control Debug] Video can play");
        };

        debugVideo.onplay = () => {
          console.log("🎮 [Control Debug] Video started playing");
        };

        debugVideo.onerror = (error) => {
          console.error("❌ [Control Debug] Video error:", error);
          overlay.style.background = "rgba(255,0,0,0.9)";
          overlay.textContent = `CONTROL TAB - ERROR! - ${streamId.substring(
            0,
            8
          )}`;
          statusDiv.textContent = "❌ Video error!";
          statusDiv.style.color = "#ff4444";
        };

        // Assemble the debug container
        container.style.position = "relative";
        container.appendChild(debugVideo);
        container.appendChild(overlay);
        container.appendChild(statusDiv);

        // Add close button
        const closeBtn = document.createElement("button");
        closeBtn.textContent = "×";
        closeBtn.style.position = "absolute";
        closeBtn.style.top = "5px";
        closeBtn.style.right = "5px";
        closeBtn.style.background = "#ff4444";
        closeBtn.style.color = "white";
        closeBtn.style.border = "none";
        closeBtn.style.borderRadius = "50%";
        closeBtn.style.width = "20px";
        closeBtn.style.height = "20px";
        closeBtn.style.cursor = "pointer";
        closeBtn.style.fontSize = "12px";
        closeBtn.onclick = () => {
          if (container.parentNode) {
            container.parentNode.removeChild(container);
          }
        };
        container.appendChild(closeBtn);

        // Add to page
        document.body.appendChild(container);

        // Auto-remove after 60 seconds
        setTimeout(() => {
          if (container.parentNode) {
            container.parentNode.removeChild(container);
            console.log(
              "🎮 [Control Debug] Auto-removed debug video after 60s"
            );
          }
        }, 60000);

        console.log(
          "🎮 [Control Debug] Debug video created and added to control tab"
        );
        console.groupEnd();
      } catch (error) {
        console.error(
          "❌ [Control Debug] Failed to create debug video:",
          error
        );
        console.groupEnd();
      }
    }

    async createDummyStream() {
      // Create a canvas with animated content as a placeholder
      const canvas = document.createElement("canvas");
      canvas.width = 640;
      canvas.height = 480;
      const ctx = canvas.getContext("2d");

      // Create animated content
      let frame = 0;
      const animate = () => {
        ctx.fillStyle = `hsl(${frame % 360}, 50%, 50%)`;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = "white";
        ctx.font = "48px Arial";
        ctx.textAlign = "center";
        ctx.fillText("POC Stream", canvas.width / 2, canvas.height / 2);
        ctx.fillText(
          `Frame: ${frame}`,
          canvas.width / 2,
          canvas.height / 2 + 60
        );

        frame++;
        requestAnimationFrame(animate);
      };
      animate();

      // Get stream from canvas
      const stream = canvas.captureStream(30); // 30 FPS

      // Add audio track (silent)
      const audioContext = new AudioContext();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      gainNode.gain.value = 0; // Silent
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      const audioStream = audioContext.createMediaStreamDestination();
      oscillator.connect(audioStream);
      oscillator.start();

      // Combine video and audio
      const combinedStream = new MediaStream([
        ...stream.getVideoTracks(),
        ...audioStream.stream.getAudioTracks(),
      ]);

      return combinedStream;
    }

    cleanupStream(streamId) {
      console.log("[POC-Streaming] Cleaning up stream:", streamId);

      const peerConnection = this.peerConnections.get(streamId);
      if (peerConnection) {
        peerConnection.close();
        this.peerConnections.delete(streamId);
      }

      this.activeStreams.delete(streamId);
    }

    setupPageListeners() {
      // Listen for page unload
      window.addEventListener("beforeunload", () => {
        console.log("[POC-Streaming] Control tab unloading...");

        // Clean up all streams
        for (const streamId of this.activeStreams.keys()) {
          this.cleanupStream(streamId);
        }

        // Close WebSocket
        if (this.websocket) {
          this.websocket.close();
        }
      });
    }
  }

  // Initialize the control tab manager
  window.pocControlTabManager = new ControlTabManager();

  console.log("[POC-Streaming] Control tab script initialized successfully");
})();

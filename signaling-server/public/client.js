class WebRTCClient {
  constructor() {
    this.ws = null;
    this.peerConnections = new Map(); // streamId -> RTCPeerConnection
    this.streams = new Map(); // streamId -> { stream, metadata }
    this.clientId = null;
    this.currentRoom = null;
    this.isConnected = false;

    this.streamsGrid = document.getElementById("streamsGrid");
    this.noStreamMessage = document.getElementById("noStreamMessage");

    this.setupEventListeners();
    this.rtcConfiguration = {
      iceServers: [
        { urls: "stun:stun.l.google.com:19302" },
        { urls: "stun:stun1.l.google.com:19302" },
      ],
    };

    this.log("Web client initialized", "info");
  }

  setupEventListeners() {
    document
      .getElementById("connectBtn")
      .addEventListener("click", () => this.connect());
    document
      .getElementById("disconnectBtn")
      .addEventListener("click", () => this.disconnect());

    // Auto-connect on Enter in room input
    document.getElementById("roomInput").addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        this.connect();
      }
    });
  }

  createPeerConnection(streamId, extensionClientId) {
    this.log(`Creating peer connection for stream: ${streamId}`, "info");

    if (this.peerConnections.has(streamId)) {
      this.log(
        `Peer connection already exists for stream: ${streamId}`,
        "warning"
      );
      return this.peerConnections.get(streamId);
    }

    const pc = new RTCPeerConnection(this.rtcConfiguration);

    pc.onicecandidate = (event) => {
      if (event.candidate && this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.sendMessage({
          type: "ice-candidate",
          candidate: event.candidate,
          targetClientId: extensionClientId,
          streamId: streamId,
        });
      }
    };

    pc.ontrack = (event) => {
      this.log(
        `Received track for stream ${streamId}: ${
          event.track.kind
        }. Stream tracks: ${event.streams[0].getTracks().length}`,
        "success"
      );

      // Only add to grid when we have both video and audio tracks (or just video if no audio)
      const stream = event.streams[0];
      const videoTracks = stream.getVideoTracks().length;
      const audioTracks = stream.getAudioTracks().length;

      this.log(
        `Stream ${streamId} tracks: video=${videoTracks}, audio=${audioTracks}`,
        "info"
      );

      // Add to grid only once when we have at least video track
      // and either no audio expected or audio track received
      if (videoTracks > 0 && !this.streams.has(streamId)) {
        this.addStreamToGrid(streamId, stream, extensionClientId);
      }
    };

    pc.onconnectionstatechange = () => {
      this.log(
        `Connection state for ${streamId}: ${pc.connectionState}`,
        "info"
      );
      this.updateStreamStatus(streamId, pc.connectionState);

      if (pc.connectionState === "connected") {
        this.updateStatus("streaming", "Streaming");
      } else if (pc.connectionState === "failed") {
        this.log(`WebRTC connection failed for stream ${streamId}`, "error");
        this.removeStreamFromGrid(streamId);
      }
    };

    this.peerConnections.set(streamId, pc);
    this.log(
      `Stored peer connection for stream: ${streamId}. Total connections: ${this.peerConnections.size}`,
      "info"
    );
    return pc;
  }

  connect() {
    const roomId =
      document.getElementById("roomInput").value.trim() || "default-room";

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.disconnect();
    }

    this.log(`Connecting to room: ${roomId}`, "info");

    // Connect to WebSocket server
    const wsUrl = `ws://${window.location.hostname}:3001`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      this.log("WebSocket connected", "success");
      this.updateWSStatus("Connected");

      // Register as client
      this.sendMessage({
        type: "register",
        clientType: "client",
      });
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.log(`Received message: ${event.data}`, "info");
        this.handleMessage(message);
      } catch (error) {
        this.log(`Invalid message: ${error.message}`, "error");
      }
    };

    this.ws.onclose = () => {
      this.log("WebSocket disconnected", "warning");
      this.updateWSStatus("Disconnected");
      this.updateStatus("disconnected", "Disconnected");
      this.isConnected = false;
      this.updateUI();
    };

    this.ws.onerror = (error) => {
      this.log(`WebSocket error: ${error}`, "error");
    };
  }

  disconnect() {
    // Close all peer connections
    for (const [streamId, pc] of this.peerConnections.entries()) {
      pc.close();
      this.log(`Closed peer connection for stream: ${streamId}`, "info");
    }
    this.peerConnections.clear();

    if (this.ws) {
      this.ws.close();
    }

    this.clearAllStreams();
    this.isConnected = false;
    this.currentRoom = null;
    this.updateUI();
    this.log("Disconnected", "info");
  }

  sendMessage(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  addStreamToGrid(streamId, stream, extensionClientId) {
    this.log(
      `Adding stream ${streamId} to grid. Current streams: ${this.streams.size}`,
      "info"
    );

    // Check if stream already exists
    if (this.streams.has(streamId)) {
      this.log(
        `Stream ${streamId} already exists in grid, skipping`,
        "warning"
      );
      return;
    }

    // Store stream info
    this.streams.set(streamId, {
      stream: stream,
      extensionClientId: extensionClientId,
      element: null,
      metadata: {},
    });

    // Create stream element
    const streamElement = this.createStreamElement(streamId, stream);
    this.streams.get(streamId).element = streamElement;

    // Add to grid
    this.streamsGrid.appendChild(streamElement);

    // Hide no-stream message
    this.noStreamMessage.style.display = "none";

    // Update UI
    this.updateStreamsInfo();
    this.log(
      `Added stream ${streamId} to grid. Total streams: ${this.streams.size}`,
      "success"
    );
  }

  removeStreamFromGrid(streamId) {
    const streamInfo = this.streams.get(streamId);
    if (streamInfo && streamInfo.element) {
      streamInfo.element.remove();
      this.streams.delete(streamId);

      // Show no-stream message if no streams left
      if (this.streams.size === 0) {
        this.noStreamMessage.style.display = "flex";
      }

      this.updateStreamsInfo();
      this.log(`Removed stream ${streamId} from grid`, "info");
    }
  }

  clearAllStreams() {
    for (const [streamId, streamInfo] of this.streams.entries()) {
      if (streamInfo.element) {
        streamInfo.element.remove();
      }
    }
    this.streams.clear();
    this.noStreamMessage.style.display = "flex";
    this.updateStreamsInfo();
  }

  createStreamElement(streamId, stream) {
    const streamItem = document.createElement("div");
    streamItem.className = "stream-item";
    streamItem.id = `stream-${streamId}`;

    const video = document.createElement("video");
    video.className = "stream-video";
    video.autoplay = true;
    video.playsinline = true;
    video.muted = false;
    video.srcObject = stream;

    const overlay = document.createElement("div");
    overlay.className = "stream-overlay";

    const title = document.createElement("div");
    title.className = "stream-title";
    title.textContent = `Stream ${streamId.substring(0, 8)}...`;

    const info = document.createElement("div");
    info.className = "stream-info";
    const videoTracks = stream.getVideoTracks().length;
    const audioTracks = stream.getAudioTracks().length;
    info.textContent = `Video: ${videoTracks}, Audio: ${audioTracks}`;

    overlay.appendChild(title);
    overlay.appendChild(info);

    const controls = document.createElement("div");
    controls.className = "stream-controls";

    const fullscreenBtn = document.createElement("button");
    fullscreenBtn.className = "stream-btn";
    fullscreenBtn.textContent = "⛶";
    fullscreenBtn.title = "Fullscreen";
    fullscreenBtn.onclick = () => this.toggleStreamFullscreen(video);

    controls.appendChild(fullscreenBtn);

    streamItem.appendChild(video);
    streamItem.appendChild(overlay);
    streamItem.appendChild(controls);

    return streamItem;
  }

  toggleStreamFullscreen(video) {
    if (!document.fullscreenElement) {
      video.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  }

  updateStreamsInfo() {
    const activeCount = this.streams.size;
    let totalVideo = 0;
    let totalAudio = 0;

    for (const [streamId, streamInfo] of this.streams.entries()) {
      if (streamInfo.stream) {
        totalVideo += streamInfo.stream.getVideoTracks().length;
        totalAudio += streamInfo.stream.getAudioTracks().length;
      }
    }

    document.getElementById("activeStreamsCount").textContent = activeCount;
    document.getElementById("totalVideoTracks").textContent = totalVideo;
    document.getElementById("totalAudioTracks").textContent = totalAudio;

    this.updateStreamsList();
  }

  updateStreamsList() {
    const streamsList = document.getElementById("streamsList");
    if (this.streams.size === 0) {
      streamsList.innerHTML =
        '<p style="color: #888; font-size: 12px;">No active streams</p>';
      return;
    }

    let html = "";
    for (const [streamId, streamInfo] of this.streams.entries()) {
      const shortId = streamId.substring(0, 8);
      html += `
        <div style="padding: 4px 0; border-bottom: 1px solid #444; font-size: 11px;">
          <div style="font-weight: bold;">${shortId}...</div>
          <div style="color: #aaa;">Client: ${
            streamInfo.extensionClientId?.substring(0, 8) || "Unknown"
          }</div>
        </div>
      `;
    }
    streamsList.innerHTML = html;
  }

  updateStreamStatus(streamId, status) {
    const streamInfo = this.streams.get(streamId);
    if (streamInfo && streamInfo.element) {
      const overlay = streamInfo.element.querySelector(".stream-overlay");
      if (overlay) {
        const statusElement =
          overlay.querySelector(".stream-status") ||
          document.createElement("div");
        statusElement.className = "stream-status";
        statusElement.style.fontSize = "10px";
        statusElement.style.color =
          status === "connected" ? "#4caf50" : "#ff4757";
        statusElement.textContent = `Status: ${status}`;

        if (!overlay.querySelector(".stream-status")) {
          overlay.appendChild(statusElement);
        }
      }
    }
  }

  handleMessage(message) {
    switch (message.type) {
      case "welcome":
        this.clientId = message.clientId;
        this.updateClientInfo();
        this.log(`Assigned client ID: ${this.clientId}`, "info");
        break;

      case "registered":
        this.log("Registered as client", "success");
        // Join the specified room
        const roomId =
          document.getElementById("roomInput").value.trim() || "default-room";
        this.sendMessage({
          type: "join-room",
          roomId: roomId,
        });
        break;

      case "room-joined":
        this.currentRoom = message.roomId;
        this.isConnected = true;
        this.updateStatus("connected", "Connected");
        this.updateClientInfo();
        this.updateUI();
        this.log(`Joined room: ${message.roomId}`, "success");
        break;

      case "client-joined":
        if (message.clientType === "extension") {
          this.extensionClientId = message.clientId;
          this.log(`Extension joined room: ${message.clientId}`, "info");
        }
        break;

      case "stream-available":
        this.log(
          `Stream available: ${message.streamId} from ${message.fromClientId}`,
          "success"
        );
        // Create peer connection for this stream
        this.createPeerConnection(message.streamId, message.fromClientId);
        // Extension will send offer, we just wait
        break;

      case "offer":
        this.handleOffer(message);
        break;

      case "ice-candidate":
        this.handleIceCandidate(message);
        break;

      case "stream-stopped":
        const stoppedStreamId = message.streamId;
        this.log(`Stream stopped: ${stoppedStreamId}`, "warning");

        // Close peer connection for this stream
        const pc = this.peerConnections.get(stoppedStreamId);
        if (pc) {
          pc.close();
          this.peerConnections.delete(stoppedStreamId);
        }

        // Remove from grid
        this.removeStreamFromGrid(stoppedStreamId);
        break;

      default:
        this.log(`Unknown message type: ${message.type}`, "warning");
    }
  }

  async handleOffer(message) {
    try {
      const streamId = message.streamId;
      this.log(
        `Received offer for stream ${streamId} from ${message.fromClientId}`,
        "info"
      );

      this.log(
        `Available peer connections: ${Array.from(
          this.peerConnections.keys()
        ).join(", ")}`,
        "info"
      );

      const pc = this.peerConnections.get(streamId);
      if (!pc) {
        this.log(
          `No peer connection found for stream ${streamId}. Available: ${Array.from(
            this.peerConnections.keys()
          ).join(", ")}`,
          "error"
        );
        throw new Error(`No peer connection found for stream ${streamId}`);
      }

      await pc.setRemoteDescription(message.offer);
      const answer = await pc.createAnswer();
      await pc.setLocalDescription(answer);

      this.sendMessage({
        type: "answer",
        answer: answer,
        targetClientId: message.fromClientId,
        streamId: streamId,
      });

      this.log(`Sent answer for stream ${streamId}`, "success");
    } catch (error) {
      this.log(`Error handling offer: ${error.message}`, "error");
    }
  }

  async handleIceCandidate(message) {
    try {
      const streamId = message.streamId;
      const pc = this.peerConnections.get(streamId);
      if (!pc) {
        this.log(`No peer connection found for stream ${streamId}`, "warning");
        return;
      }

      await pc.addIceCandidate(message.candidate);
      this.log(`Added ICE candidate for stream ${streamId}`, "info");
    } catch (error) {
      this.log(`Error adding ICE candidate: ${error.message}`, "error");
    }
  }

  updateStatus(status, text) {
    const statusDot = document.getElementById("statusDot");
    const statusText = document.getElementById("statusText");

    statusDot.className = `status-dot ${status}`;
    statusText.textContent = text;
  }

  updateWSStatus(status) {
    document.getElementById("wsStatus").textContent = status;
  }

  updateClientInfo() {
    document.getElementById("clientId").textContent = this.clientId || "-";
    document.getElementById("currentRoom").textContent =
      this.currentRoom || "-";

    // Show overall peer connection state
    const states = Array.from(this.peerConnections.values()).map(
      (pc) => pc.connectionState
    );
    const peerState = states.length > 0 ? `${states.length} connections` : "-";
    document.getElementById("peerState").textContent = peerState;
  }

  updateUI() {
    const connectBtn = document.getElementById("connectBtn");
    const disconnectBtn = document.getElementById("disconnectBtn");
    const roomInput = document.getElementById("roomInput");

    connectBtn.disabled = this.isConnected;
    disconnectBtn.disabled = !this.isConnected;
    roomInput.disabled = this.isConnected;

    this.updateClientInfo();
  }

  log(message, type = "info") {
    const logContainer = document.getElementById("logContainer");
    const logEntry = document.createElement("div");
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;

    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;

    // Keep only last 100 entries
    while (logContainer.children.length > 100) {
      logContainer.removeChild(logContainer.firstChild);
    }

    console.log(`[${type.toUpperCase()}] ${message}`);
  }
}

// Initialize client when page loads
document.addEventListener("DOMContentLoaded", () => {
  new WebRTCClient();
});

const WebSocket = require("ws");
const express = require("express");
const cors = require("cors");
const { v4: uuidv4 } = require("uuid");
const path = require("path");

const app = express();
const PORT = process.env.PORT || 3000;
const WS_PORT = process.env.WS_PORT || 3001;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, "public")));

// Store connected clients
const clients = new Map();
const rooms = new Map();
// Store active streams with metadata
const activeStreams = new Map(); // streamId -> { clientId, tabId, roomId, streamInfo }

// Create WebSocket server
const wss = new WebSocket.Server({ port: WS_PORT });

console.log(`WebSocket signaling server running on port ${WS_PORT}`);
console.log(`HTTP server running on port ${PORT}`);

// WebSocket connection handler
wss.on("connection", (ws, req) => {
  const clientId = uuidv4();
  const clientInfo = {
    id: clientId,
    ws: ws,
    type: null, // 'extension' or 'client'
    roomId: null,
    connected: true,
  };

  clients.set(clientId, clientInfo);
  console.log(`Client connected: ${clientId}`);

  // Send welcome message with client ID
  ws.send(
    JSON.stringify({
      type: "welcome",
      clientId: clientId,
      timestamp: Date.now(),
    })
  );

  ws.on("message", (data) => {
    try {
      const message = JSON.parse(data);
      handleMessage(clientInfo, message);
    } catch (error) {
      console.error("Invalid JSON message:", error);
      ws.send(
        JSON.stringify({
          type: "error",
          message: "Invalid JSON format",
        })
      );
    }
  });

  ws.on("close", () => {
    console.log(`Client disconnected: ${clientId}`);
    handleClientDisconnect(clientInfo);
    clients.delete(clientId);
  });

  ws.on("error", (error) => {
    console.error(`WebSocket error for client ${clientId}:`, error);
  });
});

function handleMessage(sender, message) {
  console.log(`Message from ${sender.id} (${sender.type}):`, message.type);

  switch (message.type) {
    case "register":
      handleRegister(sender, message);
      break;
    case "join-room":
      handleJoinRoom(sender, message);
      break;
    case "offer":
      handleOffer(sender, message);
      break;
    case "answer":
      handleAnswer(sender, message);
      break;
    case "ice-candidate":
      handleIceCandidate(sender, message);
      break;
    case "start-stream":
      handleStartStream(sender, message);
      break;
    case "stop-stream":
      handleStopStream(sender, message);
      break;
    default:
      console.warn(`Unknown message type: ${message.type}`);
  }
}

function handleRegister(client, message) {
  client.type = message.clientType; // 'extension' or 'client'
  console.log(`Client ${client.id} registered as ${client.type}`);

  client.ws.send(
    JSON.stringify({
      type: "registered",
      clientType: client.type,
      availableRooms: Array.from(rooms.keys()),
    })
  );
}

function handleJoinRoom(client, message) {
  const roomId = message.roomId || "default-room";

  // Leave current room if in one
  if (client.roomId) {
    leaveRoom(client);
  }

  // Join new room
  if (!rooms.has(roomId)) {
    rooms.set(roomId, new Set());
  }

  rooms.get(roomId).add(client.id);
  client.roomId = roomId;

  console.log(`Client ${client.id} joined room ${roomId}`);

  // Notify client
  client.ws.send(
    JSON.stringify({
      type: "room-joined",
      roomId: roomId,
      clientsInRoom: Array.from(rooms.get(roomId)),
    })
  );

  // Notify other clients in room
  broadcastToRoom(
    roomId,
    {
      type: "client-joined",
      clientId: client.id,
      clientType: client.type,
    },
    client.id
  );

  // If this is a web client joining, notify about existing streams
  if (client.type === "client") {
    const existingStreams = getAllStreamsInRoom(roomId);
    for (const stream of existingStreams) {
      client.ws.send(
        JSON.stringify({
          type: "stream-available",
          fromClientId: stream.clientId,
          streamId: stream.streamId,
          tabId: stream.tabId,
          streamInfo: stream.streamInfo,
          allStreams: existingStreams,
        })
      );
      console.log(
        `Notified client ${client.id} about existing stream ${stream.streamId}`
      );
    }
  }
}

function handleOffer(sender, message) {
  const targetClient = clients.get(message.targetClientId);
  if (targetClient && targetClient.roomId === sender.roomId) {
    targetClient.ws.send(
      JSON.stringify({
        type: "offer",
        offer: message.offer,
        fromClientId: sender.id,
        streamId: message.streamId,
      })
    );
  }
}

function handleAnswer(sender, message) {
  const targetClient = clients.get(message.targetClientId);
  if (targetClient && targetClient.roomId === sender.roomId) {
    targetClient.ws.send(
      JSON.stringify({
        type: "answer",
        answer: message.answer,
        fromClientId: sender.id,
        streamId: message.streamId,
      })
    );
  }
}

function handleIceCandidate(sender, message) {
  const targetClient = clients.get(message.targetClientId);
  if (targetClient && targetClient.roomId === sender.roomId) {
    targetClient.ws.send(
      JSON.stringify({
        type: "ice-candidate",
        candidate: message.candidate,
        fromClientId: sender.id,
        streamId: message.streamId,
      })
    );
  }
}

function handleStartStream(sender, message) {
  console.log(
    "Received start-stream message:",
    JSON.stringify(message, null, 2)
  );
  if (sender.type === "extension") {
    // Generate unique stream ID if not provided
    const streamId = message.streamId || uuidv4();
    const tabId = message.tabId || message.streamInfo?.tabId;

    // Store stream metadata
    activeStreams.set(streamId, {
      clientId: sender.id,
      tabId: tabId,
      roomId: sender.roomId,
      streamInfo: message.streamInfo,
      timestamp: Date.now(),
    });

    console.log(
      `Stream started: ${streamId} from client ${sender.id} (tab ${tabId})`
    );

    // Broadcast to all clients in room with stream metadata
    broadcastToRoom(
      sender.roomId,
      {
        type: "stream-available",
        fromClientId: sender.id,
        streamId: streamId,
        tabId: tabId,
        streamInfo: message.streamInfo,
        allStreams: getAllStreamsInRoom(sender.roomId),
      },
      sender.id
    );
  }
}

function handleStopStream(sender, message) {
  if (sender.type === "extension") {
    const streamId = message.streamId;
    const tabId = message.tabId;

    // Remove stream from active streams
    let removedStream = null;
    if (streamId) {
      removedStream = activeStreams.get(streamId);
      activeStreams.delete(streamId);
    } else {
      // If no streamId provided, remove all streams from this client/tab
      for (const [id, stream] of activeStreams.entries()) {
        if (
          stream.clientId === sender.id &&
          (!tabId || stream.tabId === tabId)
        ) {
          activeStreams.delete(id);
          removedStream = stream;
          break;
        }
      }
    }

    if (removedStream) {
      console.log(
        `Stream stopped: ${streamId || "unknown"} from client ${
          sender.id
        } (tab ${tabId || removedStream.tabId})`
      );
    }

    broadcastToRoom(
      sender.roomId,
      {
        type: "stream-stopped",
        fromClientId: sender.id,
        streamId: streamId,
        tabId: tabId,
        allStreams: getAllStreamsInRoom(sender.roomId),
      },
      sender.id
    );
  }
}

function getAllStreamsInRoom(roomId) {
  const streams = [];
  for (const [streamId, stream] of activeStreams.entries()) {
    if (stream.roomId === roomId) {
      streams.push({
        streamId: streamId,
        clientId: stream.clientId,
        tabId: stream.tabId,
        streamInfo: stream.streamInfo,
        timestamp: stream.timestamp,
      });
    }
  }
  return streams;
}

function broadcastToRoom(roomId, message, excludeClientId = null) {
  if (!rooms.has(roomId)) return;

  const room = rooms.get(roomId);
  room.forEach((clientId) => {
    if (clientId !== excludeClientId) {
      const client = clients.get(clientId);
      if (client && client.connected) {
        client.ws.send(JSON.stringify(message));
      }
    }
  });
}

function leaveRoom(client) {
  if (client.roomId && rooms.has(client.roomId)) {
    const room = rooms.get(client.roomId);
    room.delete(client.id);

    // Notify other clients
    broadcastToRoom(
      client.roomId,
      {
        type: "client-left",
        clientId: client.id,
      },
      client.id
    );

    // Clean up empty rooms
    if (room.size === 0) {
      rooms.delete(client.roomId);
    }

    client.roomId = null;
  }
}

function handleClientDisconnect(client) {
  client.connected = false;

  // Clean up any streams from this client
  const streamsToRemove = [];
  for (const [streamId, stream] of activeStreams.entries()) {
    if (stream.clientId === client.id) {
      streamsToRemove.push(streamId);
    }
  }

  streamsToRemove.forEach((streamId) => {
    const stream = activeStreams.get(streamId);
    activeStreams.delete(streamId);
    console.log(
      `Cleaned up stream ${streamId} from disconnected client ${client.id}`
    );

    // Notify room about stream removal
    if (stream && stream.roomId) {
      broadcastToRoom(
        stream.roomId,
        {
          type: "stream-stopped",
          fromClientId: client.id,
          streamId: streamId,
          tabId: stream.tabId,
          allStreams: getAllStreamsInRoom(stream.roomId),
        },
        client.id
      );
    }
  });

  leaveRoom(client);
}

// HTTP endpoints for status and debugging
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "index.html"));
});

app.get("/status", (req, res) => {
  res.json({
    connectedClients: clients.size,
    activeRooms: rooms.size,
    activeStreams: activeStreams.size,
    clients: Array.from(clients.values()).map((c) => ({
      id: c.id,
      type: c.type,
      roomId: c.roomId,
      connected: c.connected,
    })),
    rooms: Array.from(rooms.entries()).map(([roomId, clientIds]) => ({
      roomId,
      clients: Array.from(clientIds),
      streams: getAllStreamsInRoom(roomId),
    })),
    streams: Array.from(activeStreams.entries()).map(([streamId, stream]) => ({
      streamId,
      clientId: stream.clientId,
      tabId: stream.tabId,
      roomId: stream.roomId,
      timestamp: stream.timestamp,
    })),
  });
});

app.listen(PORT, () => {
  console.log(`HTTP server listening on http://localhost:${PORT}`);
  console.log(`WebSocket server listening on ws://localhost:${WS_PORT}`);
});

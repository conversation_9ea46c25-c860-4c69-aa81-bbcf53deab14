/**
 * WebRTC Black Video Debugging Utilities
 * 
 * Comprehensive debugging tools for diagnosing black video issues
 * in WebRTC tab streaming implementations.
 */

class WebRTCVideoDebugger {
  constructor() {
    this.debug = true;
    this.trackHistory = new Map();
    this.streamHistory = new Map();
    this.videoElementHistory = new Map();
  }

  /**
   * Debug track capture at source (target tab)
   */
  debugTrackCapture(stream, context = "unknown") {
    console.group(`🎥 [Track Capture Debug] ${context}`);
    
    if (!stream) {
      console.error("❌ Stream is null/undefined");
      console.groupEnd();
      return false;
    }

    console.log("📊 Stream Info:", {
      id: stream.id,
      active: stream.active,
      trackCount: stream.getTracks().length
    });

    // Debug each track
    stream.getTracks().forEach((track, index) => {
      console.log(`🎬 Track ${index + 1}:`, {
        kind: track.kind,
        id: track.id,
        label: track.label,
        enabled: track.enabled,
        muted: track.muted,
        readyState: track.readyState,
        settings: track.getSettings ? track.getSettings() : "N/A",
        constraints: track.getConstraints ? track.getConstraints() : "N/A"
      });

      // Check for common issues
      if (track.readyState === 'ended') {
        console.error(`❌ Track ${track.id} is ended!`);
      }
      if (track.muted) {
        console.warn(`⚠️ Track ${track.id} is muted!`);
      }
      if (!track.enabled) {
        console.warn(`⚠️ Track ${track.id} is disabled!`);
      }

      // Store track history
      this.trackHistory.set(track.id, {
        timestamp: Date.now(),
        context,
        track: track,
        initialState: {
          enabled: track.enabled,
          muted: track.muted,
          readyState: track.readyState
        }
      });
    });

    console.groupEnd();
    return true;
  }

  /**
   * Debug track relay/proxy logic (control tab)
   */
  debugTrackRelay(sourceStream, targetPeerConnection, context = "relay") {
    console.group(`🔄 [Track Relay Debug] ${context}`);

    if (!sourceStream || !targetPeerConnection) {
      console.error("❌ Missing sourceStream or targetPeerConnection");
      console.groupEnd();
      return false;
    }

    console.log("📡 Relay Info:", {
      sourceStreamId: sourceStream.id,
      sourceTrackCount: sourceStream.getTracks().length,
      peerConnectionState: targetPeerConnection.connectionState,
      signalingState: targetPeerConnection.signalingState
    });

    // Check each track being added
    sourceStream.getTracks().forEach((track, index) => {
      console.log(`🎬 Relaying Track ${index + 1}:`, {
        kind: track.kind,
        id: track.id,
        enabled: track.enabled,
        muted: track.muted,
        readyState: track.readyState
      });

      // Verify track is properly added to peer connection
      const senders = targetPeerConnection.getSenders();
      const trackSender = senders.find(sender => sender.track && sender.track.id === track.id);
      
      if (trackSender) {
        console.log(`✅ Track ${track.id} found in peer connection senders`);
      } else {
        console.error(`❌ Track ${track.id} NOT found in peer connection senders`);
      }
    });

    console.groupEnd();
    return true;
  }

  /**
   * Debug video element rendering (web client)
   */
  debugVideoElement(videoElement, stream, context = "client") {
    console.group(`📺 [Video Element Debug] ${context}`);

    if (!videoElement) {
      console.error("❌ Video element is null/undefined");
      console.groupEnd();
      return false;
    }

    console.log("🎮 Video Element Info:", {
      tagName: videoElement.tagName,
      autoplay: videoElement.autoplay,
      muted: videoElement.muted,
      playsinline: videoElement.playsinline,
      controls: videoElement.controls,
      currentTime: videoElement.currentTime,
      duration: videoElement.duration,
      videoWidth: videoElement.videoWidth,
      videoHeight: videoElement.videoHeight,
      readyState: videoElement.readyState,
      networkState: videoElement.networkState,
      paused: videoElement.paused,
      ended: videoElement.ended
    });

    if (stream) {
      console.log("📊 Assigned Stream Info:", {
        id: stream.id,
        active: stream.active,
        trackCount: stream.getTracks().length
      });

      // Check if srcObject is properly set
      if (videoElement.srcObject === stream) {
        console.log("✅ srcObject correctly assigned");
      } else {
        console.error("❌ srcObject mismatch!", {
          expected: stream.id,
          actual: videoElement.srcObject?.id || "null"
        });
      }

      // Debug each track in the stream
      stream.getTracks().forEach((track, index) => {
        console.log(`🎬 Stream Track ${index + 1}:`, {
          kind: track.kind,
          id: track.id,
          enabled: track.enabled,
          muted: track.muted,
          readyState: track.readyState
        });
      });
    }

    // Check for common video element issues
    if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
      console.warn("⚠️ Video dimensions are 0x0 - possible black screen");
    }

    if (videoElement.readyState < 2) {
      console.warn("⚠️ Video not ready - readyState:", videoElement.readyState);
    }

    console.groupEnd();
    return true;
  }

  /**
   * Monitor track state changes
   */
  monitorTrackChanges(track, context = "unknown") {
    if (!track) return;

    const trackId = track.id;
    console.log(`🔍 [Track Monitor] Starting monitoring for ${trackId} in ${context}`);

    // Monitor track events
    track.addEventListener('ended', () => {
      console.error(`❌ [Track Monitor] Track ${trackId} ended in ${context}`);
    });

    track.addEventListener('mute', () => {
      console.warn(`⚠️ [Track Monitor] Track ${trackId} muted in ${context}`);
    });

    track.addEventListener('unmute', () => {
      console.log(`✅ [Track Monitor] Track ${trackId} unmuted in ${context}`);
    });

    // Periodic state check
    const intervalId = setInterval(() => {
      const history = this.trackHistory.get(trackId);
      if (history) {
        const currentState = {
          enabled: track.enabled,
          muted: track.muted,
          readyState: track.readyState
        };

        // Check for state changes
        if (JSON.stringify(currentState) !== JSON.stringify(history.initialState)) {
          console.log(`🔄 [Track Monitor] State change for ${trackId}:`, {
            previous: history.initialState,
            current: currentState
          });
          history.initialState = currentState;
        }
      }

      // Stop monitoring if track is ended
      if (track.readyState === 'ended') {
        clearInterval(intervalId);
        console.log(`🛑 [Track Monitor] Stopped monitoring ${trackId} (track ended)`);
      }
    }, 1000);

    return intervalId;
  }

  /**
   * Debug WebRTC peer connection stats
   */
  async debugPeerConnectionStats(peerConnection, context = "unknown") {
    if (!peerConnection) return;

    console.group(`📊 [Peer Connection Stats] ${context}`);

    try {
      const stats = await peerConnection.getStats();
      
      stats.forEach((report) => {
        if (report.type === 'outbound-rtp' && report.mediaType === 'video') {
          console.log("📤 Outbound Video Stats:", {
            bytesSent: report.bytesSent,
            packetsSent: report.packetsSent,
            framesSent: report.framesSent,
            framesPerSecond: report.framesPerSecond,
            frameWidth: report.frameWidth,
            frameHeight: report.frameHeight
          });
        }
        
        if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
          console.log("📥 Inbound Video Stats:", {
            bytesReceived: report.bytesReceived,
            packetsReceived: report.packetsReceived,
            framesReceived: report.framesReceived,
            framesPerSecond: report.framesPerSecond,
            frameWidth: report.frameWidth,
            frameHeight: report.frameHeight,
            framesDropped: report.framesDropped
          });
        }
      });
    } catch (error) {
      console.error("❌ Failed to get peer connection stats:", error);
    }

    console.groupEnd();
  }

  /**
   * Comprehensive video debugging
   */
  async debugFullVideoPath(sourceStream, relayPeerConnection, clientVideoElement) {
    console.group("🔍 [Full Video Path Debug]");
    
    // 1. Debug source capture
    this.debugTrackCapture(sourceStream, "source");
    
    // 2. Debug relay
    if (relayPeerConnection) {
      this.debugTrackRelay(sourceStream, relayPeerConnection, "relay");
      await this.debugPeerConnectionStats(relayPeerConnection, "relay");
    }
    
    // 3. Debug client video element
    if (clientVideoElement) {
      this.debugVideoElement(clientVideoElement, clientVideoElement.srcObject, "client");
    }
    
    console.groupEnd();
  }

  /**
   * Quick diagnostic for black video
   */
  quickDiagnosis(videoElement) {
    console.group("⚡ [Quick Black Video Diagnosis]");
    
    const issues = [];
    
    if (!videoElement) {
      issues.push("Video element is null/undefined");
    } else {
      if (!videoElement.srcObject) {
        issues.push("No srcObject assigned to video element");
      }
      
      if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
        issues.push("Video dimensions are 0x0");
      }
      
      if (videoElement.readyState < 2) {
        issues.push(`Video not ready (readyState: ${videoElement.readyState})`);
      }
      
      if (videoElement.paused && !videoElement.autoplay) {
        issues.push("Video is paused and autoplay is disabled");
      }
      
      if (videoElement.srcObject) {
        const stream = videoElement.srcObject;
        if (!stream.active) {
          issues.push("Stream is not active");
        }
        
        const videoTracks = stream.getVideoTracks();
        if (videoTracks.length === 0) {
          issues.push("No video tracks in stream");
        } else {
          videoTracks.forEach((track, index) => {
            if (track.readyState === 'ended') {
              issues.push(`Video track ${index + 1} is ended`);
            }
            if (track.muted) {
              issues.push(`Video track ${index + 1} is muted`);
            }
            if (!track.enabled) {
              issues.push(`Video track ${index + 1} is disabled`);
            }
          });
        }
      }
    }
    
    if (issues.length === 0) {
      console.log("✅ No obvious issues found");
    } else {
      console.error("❌ Issues found:", issues);
    }
    
    console.groupEnd();
    return issues;
  }
}

// Export for use
if (typeof window !== 'undefined') {
  window.WebRTCVideoDebugger = WebRTCVideoDebugger;
}
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WebRTCVideoDebugger;
}

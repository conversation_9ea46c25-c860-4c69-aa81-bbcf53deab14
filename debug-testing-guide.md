# WebRTC Black Video Debug Testing Guide

## 🚀 Quick Start Testing

### Step 1: Start the System
```bash
cd poc-streaming-system
npm start
```

### Step 2: Open Browser Tabs
1. **Control Tab**: Will open automatically
2. **Web Client**: Navigate to `http://localhost:3001`
3. **Target Tab**: Open any website (e.g., `https://example.com`)

### Step 3: Monitor Debug Output

#### In Target Tab Console (F12):
Look for these debug groups:
- `🎥 [POC-Streamer] Capture Attempt #1`
- `📊 [POC-Streamer] Stream Debug Analysis`
- `🎮 [POC-Streamer] Creating Debug Test Video`

**Key Things to Check:**
```javascript
// Should see this if capture is working:
✅ [POC-Streamer] getDisplayMedia successful
✅ Track 1 looks healthy

// RED FLAGS - indicates black video source:
❌ [Debug Video] ZERO DIMENSIONS DETECTED - BLACK SCREEN LIKELY!
⚠️ Track 1 issues: ["Track is muted", "No video source label"]
```

#### In Control Tab Console:
Look for:
- `🔄 [POC-Streaming] Control Tab Relay Debug`
- `✅ All tracks successfully forwarded`

**RED FLAGS:**
```javascript
❌ Track count mismatch!
❌ Track 1 issues before relay: ["Track is ended"]
```

#### In Web Client Console:
Look for:
- `📺 [WebClient] Track Received Debug`
- `📺 [WebClient] Display Stream Debug`
- `🎮 [WebClient] Video Element Assignment Debug`

**RED FLAGS:**
```javascript
❌ [Video Debug] ZERO DIMENSIONS - BLACK SCREEN DETECTED!
❌ Display Track 1 issues: ["Track is muted"]
⚠️ [Video Debug] Video dimensions still 0x0
```

## 🔍 Specific Debug Scenarios

### Scenario 1: Target Tab Capture Issues

**Symptoms:**
- No debug test video appears in target tab
- Zero dimensions in capture debug

**Debug Commands:**
```javascript
// In target tab console:
// Test if getDisplayMedia works at all
navigator.mediaDevices.getDisplayMedia({video: true})
  .then(stream => {
    console.log('Manual capture test:', {
      active: stream.active,
      tracks: stream.getTracks().length,
      videoTracks: stream.getVideoTracks().length
    });
    
    // Create test video
    const video = document.createElement('video');
    video.srcObject = stream;
    video.autoplay = true;
    video.muted = true;
    video.style.position = 'fixed';
    video.style.top = '10px';
    video.style.left = '10px';
    video.style.width = '200px';
    video.style.border = '2px solid blue';
    video.style.zIndex = '999999';
    
    video.onloadedmetadata = () => {
      console.log('Manual test video dimensions:', video.videoWidth, 'x', video.videoHeight);
    };
    
    document.body.appendChild(video);
  })
  .catch(console.error);
```

### Scenario 2: Control Tab Relay Issues

**Symptoms:**
- Target capture works but tracks not forwarded
- Track count mismatch errors

**Debug Commands:**
```javascript
// In control tab console:
// Check peer connections
if (window.POCControlTab) {
  const control = window.POCControlTab;
  console.log('Target connections:', control.targetConnections.size);
  console.log('Client connections:', control.peerConnections.size);
  
  // Check specific connection
  for (const [streamId, pc] of control.targetConnections) {
    console.log(`Target PC ${streamId}:`, {
      connectionState: pc.connectionState,
      signalingState: pc.signalingState,
      receivers: pc.getReceivers().length
    });
  }
  
  for (const [streamId, pc] of control.peerConnections) {
    console.log(`Client PC ${streamId}:`, {
      connectionState: pc.connectionState,
      signalingState: pc.signalingState,
      senders: pc.getSenders().length
    });
  }
}
```

### Scenario 3: Web Client Display Issues

**Symptoms:**
- Tracks received but video element shows black
- Video dimensions are 0x0

**Debug Commands:**
```javascript
// In web client console:
// Check all video elements
document.querySelectorAll('video').forEach((video, i) => {
  console.log(`Video ${i+1}:`, {
    srcObject: video.srcObject?.id || 'none',
    autoplay: video.autoplay,
    muted: video.muted,
    readyState: video.readyState,
    videoWidth: video.videoWidth,
    videoHeight: video.videoHeight,
    paused: video.paused
  });
  
  if (video.srcObject) {
    console.log(`Video ${i+1} stream:`, {
      active: video.srcObject.active,
      tracks: video.srcObject.getTracks().map(t => ({
        kind: t.kind,
        enabled: t.enabled,
        muted: t.muted,
        readyState: t.readyState
      }))
    });
  }
});

// Force refresh video elements
document.querySelectorAll('video').forEach(video => {
  if (video.srcObject) {
    const stream = video.srcObject;
    video.srcObject = null;
    setTimeout(() => {
      video.srcObject = stream;
      video.play().catch(console.error);
    }, 100);
  }
});
```

## 🎯 Common Issues & Solutions

### Issue 1: `preferCurrentTab` Not Working
**Symptoms:** Capture succeeds but shows screen picker instead of current tab

**Solution:**
```javascript
// Try without preferCurrentTab
const stream = await navigator.mediaDevices.getDisplayMedia({
  video: { width: 1280, height: 720 }
  // Remove preferCurrentTab: true
});
```

### Issue 2: Track Muted After Capture
**Symptoms:** Track shows as muted in debug output

**Solution:**
```javascript
// In target tab, after capture:
stream.getVideoTracks().forEach(track => {
  if (track.muted) {
    console.log('Attempting to unmute track:', track.id);
    // Note: You can't programmatically unmute, user must allow
  }
});
```

### Issue 3: Video Element Not Playing
**Symptoms:** Stream assigned but video doesn't start

**Solution:**
```javascript
// Force play all video elements
document.querySelectorAll('video').forEach(video => {
  video.play().catch(e => console.log('Play failed:', e.message));
});
```

## 📊 Debug Output Analysis

### Good Output Example:
```
🎥 [POC-Streamer] Capture Attempt #1
  ✅ [POC-Streamer] getDisplayMedia successful
  📊 Stream Info: {id: "abc123", active: true, tracks: 1, videoTracks: 1}
  🎬 Track 1 (video): {enabled: true, muted: false, readyState: "live"}
  ✅ Track 1 looks healthy
  🎮 [Debug Video] Valid dimensions detected
  
🔄 [POC-Streaming] Control Tab Relay Debug
  ✅ All tracks successfully forwarded
  
📺 [WebClient] Track Received Debug
  ✅ Track 1 looks healthy
  🎮 [Video Debug] Valid video dimensions detected
```

### Bad Output Example:
```
🎥 [POC-Streamer] Capture Attempt #1
  ✅ [POC-Streamer] getDisplayMedia successful
  📊 Stream Info: {id: "abc123", active: true, tracks: 1, videoTracks: 1}
  🎬 Track 1 (video): {enabled: true, muted: true, readyState: "live"}
  ⚠️ Track 1 issues: ["Track is muted"]
  ❌ [Debug Video] ZERO DIMENSIONS DETECTED - BLACK SCREEN LIKELY!
```

## 🚨 Emergency Debug Commands

If you need to quickly diagnose the issue:

```javascript
// Run this in any console to get a quick overview:
function quickDiagnosis() {
  console.group('🚨 Quick WebRTC Diagnosis');
  
  // Check if we're in the right context
  console.log('Context:', {
    url: window.location.href,
    title: document.title,
    hasGetDisplayMedia: !!navigator.mediaDevices?.getDisplayMedia,
    hasGetUserMedia: !!navigator.mediaDevices?.getUserMedia
  });
  
  // Check video elements
  const videos = document.querySelectorAll('video');
  console.log(`Found ${videos.length} video elements`);
  
  videos.forEach((video, i) => {
    const issues = [];
    if (!video.srcObject) issues.push('No stream');
    if (video.videoWidth === 0) issues.push('Zero width');
    if (video.videoHeight === 0) issues.push('Zero height');
    if (video.paused && video.autoplay) issues.push('Paused despite autoplay');
    
    console.log(`Video ${i+1}:`, issues.length ? `❌ ${issues.join(', ')}` : '✅ OK');
  });
  
  console.groupEnd();
}

// Run it
quickDiagnosis();
```

## 📝 Next Steps

1. **Start the system** and open all three tabs
2. **Monitor console output** in each tab during streaming
3. **Look for the specific error patterns** mentioned above
4. **Use the debug commands** to investigate further
5. **Report back** with the specific error messages you see

The enhanced debugging will show you exactly where in the pipeline the video content is being lost!
